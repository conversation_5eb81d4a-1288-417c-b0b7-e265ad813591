"""
高性能缓存管理器
支持多层缓存：内存缓存 + 数据库缓存
针对TiDB Cloud优化的缓存策略
"""

import json
import time
import hashlib
import logging
from typing import Any, Dict, List, Optional, Union
from functools import wraps
import aiomysql
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class PerformanceCache:
    """高性能多层缓存管理器"""
    
    def __init__(self):
        # 内存缓存（LRU）
        self._memory_cache: Dict[str, Dict[str, Any]] = {}
        self._memory_access_times: Dict[str, float] = {}
        self._max_memory_items = 1000
        
        # 缓存统计
        self._stats = {
            'memory_hits': 0,
            'db_hits': 0,
            'misses': 0,
            'total_requests': 0
        }
    
    def _generate_cache_key(self, prefix: str, **kwargs) -> str:
        """生成缓存键"""
        key_data = f"{prefix}:{json.dumps(kwargs, sort_keys=True)}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _cleanup_memory_cache(self):
        """清理内存缓存，保持在限制范围内"""
        if len(self._memory_cache) <= self._max_memory_items:
            return
        
        # 移除最旧的20%项目
        items_to_remove = len(self._memory_cache) - int(self._max_memory_items * 0.8)
        sorted_items = sorted(
            self._memory_access_times.items(), 
            key=lambda x: x[1]
        )
        
        for key, _ in sorted_items[:items_to_remove]:
            self._memory_cache.pop(key, None)
            self._memory_access_times.pop(key, None)
    
    async def get(self, pool: aiomysql.Pool, cache_key: str) -> Optional[Any]:
        """获取缓存数据"""
        self._stats['total_requests'] += 1
        current_time = time.time()
        
        # 1. 检查内存缓存
        if cache_key in self._memory_cache:
            cache_data = self._memory_cache[cache_key]
            if cache_data['expires_at'] > current_time:
                self._memory_access_times[cache_key] = current_time
                self._stats['memory_hits'] += 1
                logger.debug(f"内存缓存命中: {cache_key}")
                return cache_data['data']
            else:
                # 过期，删除
                del self._memory_cache[cache_key]
                del self._memory_access_times[cache_key]
        
        # 2. 检查数据库缓存
        try:
            async with pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    await cursor.execute(
                        "SELECT cache_value, expires_at FROM cache_data WHERE cache_key = %s AND expires_at > NOW()",
                        (cache_key,)
                    )
                    result = await cursor.fetchone()
                    
                    if result:
                        self._stats['db_hits'] += 1
                        data = json.loads(result['cache_value'])
                        
                        # 回填到内存缓存
                        expires_timestamp = result['expires_at'].timestamp()
                        self._memory_cache[cache_key] = {
                            'data': data,
                            'expires_at': expires_timestamp
                        }
                        self._memory_access_times[cache_key] = current_time
                        self._cleanup_memory_cache()
                        
                        logger.debug(f"数据库缓存命中: {cache_key}")
                        return data
        except Exception as e:
            logger.warning(f"数据库缓存查询失败: {e}")
        
        # 缓存未命中
        self._stats['misses'] += 1
        return None
    
    async def set(self, pool: aiomysql.Pool, cache_key: str, data: Any, ttl_seconds: int = 3600):
        """设置缓存数据"""
        current_time = time.time()
        expires_at = current_time + ttl_seconds
        
        # 1. 设置内存缓存
        self._memory_cache[cache_key] = {
            'data': data,
            'expires_at': expires_at
        }
        self._memory_access_times[cache_key] = current_time
        self._cleanup_memory_cache()
        
        # 2. 设置数据库缓存
        try:
            async with pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    expires_datetime = datetime.fromtimestamp(expires_at)
                    await cursor.execute(
                        """
                        INSERT INTO cache_data (cache_key, cache_value, expires_at, cache_provider)
                        VALUES (%s, %s, %s, 'performance_cache')
                        ON DUPLICATE KEY UPDATE 
                            cache_value = VALUES(cache_value),
                            expires_at = VALUES(expires_at)
                        """,
                        (cache_key, json.dumps(data), expires_datetime)
                    )
        except Exception as e:
            logger.warning(f"数据库缓存设置失败: {e}")
    
    async def delete(self, pool: aiomysql.Pool, cache_key: str):
        """删除缓存数据"""
        # 删除内存缓存
        self._memory_cache.pop(cache_key, None)
        self._memory_access_times.pop(cache_key, None)
        
        # 删除数据库缓存
        try:
            async with pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("DELETE FROM cache_data WHERE cache_key = %s", (cache_key,))
        except Exception as e:
            logger.warning(f"数据库缓存删除失败: {e}")
    
    async def clear_expired(self, pool: aiomysql.Pool):
        """清理过期缓存"""
        current_time = time.time()
        
        # 清理内存缓存
        expired_keys = [
            key for key, cache_data in self._memory_cache.items()
            if cache_data['expires_at'] <= current_time
        ]
        for key in expired_keys:
            del self._memory_cache[key]
            del self._memory_access_times[key]
        
        # 清理数据库缓存
        try:
            async with pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("DELETE FROM cache_data WHERE expires_at <= NOW()")
                    deleted_count = cursor.rowcount
                    if deleted_count > 0:
                        logger.info(f"清理了 {deleted_count} 条过期缓存记录")
        except Exception as e:
            logger.warning(f"数据库缓存清理失败: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total = self._stats['total_requests']
        if total == 0:
            hit_rate = 0
        else:
            hit_rate = (self._stats['memory_hits'] + self._stats['db_hits']) / total * 100
        
        return {
            **self._stats,
            'hit_rate': f"{hit_rate:.2f}%",
            'memory_cache_size': len(self._memory_cache)
        }

# 全局缓存实例
performance_cache = PerformanceCache()

def cache_result(cache_prefix: str, ttl_seconds: int = 3600):
    """缓存装饰器，用于自动缓存函数结果"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 提取pool参数
            pool = None
            for arg in args:
                if isinstance(arg, aiomysql.Pool):
                    pool = arg
                    break
            
            if pool is None:
                # 如果没有找到pool，直接执行函数
                return await func(*args, **kwargs)
            
            # 生成缓存键
            cache_key = performance_cache._generate_cache_key(
                cache_prefix, 
                func_name=func.__name__,
                args=str(args[1:]),  # 排除pool参数
                kwargs=kwargs
            )
            
            # 尝试从缓存获取
            cached_result = await performance_cache.get(pool, cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            start_time = time.time()
            result = await func(*args, **kwargs)
            execution_time = (time.time() - start_time) * 1000
            
            # 缓存结果
            await performance_cache.set(pool, cache_key, result, ttl_seconds)
            
            logger.info(f"函数 {func.__name__} 执行耗时 {execution_time:.2f}ms，结果已缓存")
            return result
        
        return wrapper
    return decorator

async def get_cache_stats(pool: aiomysql.Pool) -> Dict[str, Any]:
    """获取缓存统计信息"""
    stats = performance_cache.get_stats()
    
    # 添加数据库缓存统计
    try:
        async with pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute("""
                    SELECT 
                        COUNT(*) as total_db_cache,
                        COUNT(CASE WHEN expires_at > NOW() THEN 1 END) as valid_db_cache,
                        COUNT(CASE WHEN expires_at <= NOW() THEN 1 END) as expired_db_cache
                    FROM cache_data
                """)
                db_stats = await cursor.fetchone()
                stats.update(db_stats)
    except Exception as e:
        logger.warning(f"获取数据库缓存统计失败: {e}")
    
    return stats
