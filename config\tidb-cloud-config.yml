# TiDB Cloud 数据库配置示例
# 请根据您的TiDB Cloud实例信息修改以下配置

# 服务器配置
server:
  host: "0.0.0.0"  # 监听所有接口，适合宝塔部署
  port: 7768

# TiDB Cloud数据库配置
database:
  # TiDB Cloud连接信息（请替换为您的实际信息）
  host: "gateway01.ap-southeast-1.prod.aws.tidbcloud.com"  # TiDB Cloud网关地址
  port: 4000  # TiDB Cloud默认端口为4000
  user: "your_tidb_username"  # 您的TiDB Cloud用户名
  password: "your_tidb_password"  # 您的TiDB Cloud密码
  name: "your_database_name"  # 数据库名称

  # TLS/SSL安全连接配置（TiDB Cloud必需）
  ssl_enabled: true  # 必须启用SSL/TLS连接
  ssl_verify_cert: true  # 推荐启用证书验证
  ssl_ca_path: null  # TiDB Cloud使用公共CA，通常不需要自定义CA证书
  ssl_cert_path: null  # TiDB Cloud单向认证，不需要客户端证书
  ssl_key_path: null  # TiDB Cloud单向认证，不需要客户端私钥

# JWT (JSON Web Token) 配置
jwt:
  secret_key: "your_jwt_secret_key_here"  # 请修改为复杂且唯一的密钥
  algorithm: "HS256"
  access_token_expire_minutes: 1440  # 令牌有效期（分钟）

# 管理员配置
admin:
  initial_user: "admin"  # 初始管理员用户名
  initial_password: ""  # 留空将自动生成随机密码

# Bangumi API配置
bangumi:
  base_url: "https://api.bgm.tv"
  user_agent: "LoveStory/1.0"

# 豆瓣API配置
douban:
  base_url: "https://frodo.douban.com"
  user_agent: "LoveStory/1.0"

# TiDB Cloud连接说明：
# 1. 登录TiDB Cloud控制台
# 2. 选择您的集群
# 3. 点击"Connect"按钮
# 4. 选择"General"连接方式
# 5. 复制连接信息到上述配置中
# 6. 确保启用SSL连接（TiDB Cloud要求）
