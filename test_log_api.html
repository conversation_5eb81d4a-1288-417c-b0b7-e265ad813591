<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志API测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log-container { 
            border: 1px solid #ccc; 
            padding: 10px; 
            height: 400px; 
            overflow-y: auto; 
            background: #f5f5f5; 
            white-space: pre-wrap;
            font-family: monospace;
        }
        .controls { margin-bottom: 10px; }
        button { margin-right: 10px; padding: 5px 10px; }
        .status { margin-top: 10px; padding: 10px; background: #e7f3ff; }
    </style>
</head>
<body>
    <h1>日志API测试工具</h1>
    
    <div class="controls">
        <button onclick="testLogAPI()">测试日志API</button>
        <button onclick="startAutoRefresh()">开始自动刷新</button>
        <button onclick="stopAutoRefresh()">停止自动刷新</button>
        <button onclick="clearLogs()">清空显示</button>
    </div>
    
    <div class="status" id="status">
        状态：等待操作...
    </div>
    
    <h3>日志内容：</h3>
    <div class="log-container" id="logContainer">
        点击"测试日志API"开始...
    </div>

    <script>
        let refreshInterval = null;
        let lastContent = '';
        let refreshCount = 0;

        function updateStatus(message) {
            document.getElementById('status').textContent = `状态：${message}`;
            console.log(`[状态] ${message}`);
        }

        async function testLogAPI() {
            const container = document.getElementById('logContainer');
            updateStatus('正在获取日志...');
            
            try {
                // 获取token
                const token = localStorage.getItem('danmu_api_token');
                if (!token) {
                    updateStatus('错误：未找到API token，请先登录');
                    container.textContent = '错误：未找到API token，请先登录主应用';
                    return;
                }

                // 调用API
                const response = await fetch('/api/ui/logs', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const logs = await response.json();
                refreshCount++;
                
                updateStatus(`成功获取 ${logs.length} 条日志 (刷新 #${refreshCount})`);
                
                // 显示日志
                const newContent = logs.join('\n');
                const contentChanged = lastContent !== newContent;
                
                console.log(`[测试] 获取到 ${logs.length} 条日志，内容是否改变: ${contentChanged}`);
                
                container.textContent = newContent;
                lastContent = newContent;
                
                // 滚动到顶部显示最新日志
                container.scrollTop = 0;
                
            } catch (error) {
                updateStatus(`错误：${error.message}`);
                container.textContent = `API调用失败：${error.message}`;
                console.error('API调用失败:', error);
            }
        }

        function startAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
            
            updateStatus('开始自动刷新 (每3秒)');
            testLogAPI(); // 立即执行一次
            refreshInterval = setInterval(testLogAPI, 3000);
        }

        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
                updateStatus('已停止自动刷新');
            }
        }

        function clearLogs() {
            document.getElementById('logContainer').textContent = '';
            lastContent = '';
            refreshCount = 0;
            updateStatus('已清空日志显示');
        }

        // 页面加载时的提示
        window.addEventListener('load', () => {
            updateStatus('页面已加载，请先在主应用中登录，然后点击"测试日志API"');
        });
    </script>
</body>
</html>
