# LoveStory弹幕服务配置文件
# 所有配置项都可以在此文件中设置，不再需要环境变量

# ==================== 服务器配置 ====================
server:
  host: "0.0.0.0"  # 服务监听地址
  port: 7768       # 服务监听端口

# ==================== 数据库配置 ====================
database:
  # 远程数据库连接信息（推荐用于生产环境）
  host: "gateway01.ap-southeast-1.prod.aws.tidbcloud.com"
  port: 4000
  user: "2bmga6guXNxtZeu.root"
  password: "o61AeN7aExII4DD"
  name: "test"

  # 数据库类型配置（重要：解决TiDB兼容性问题）
  # 选项: auto（自动检测）, mysql（MySQL/MariaDB）, tidb（TiDB）
  db_type: "tidb"  # 手动指定为TiDB以避免兼容性问题

  # TLS/SSL安全连接配置（TiDB Cloud兼容）
  ssl_enabled: true
  ssl_verify_cert: true  # 临时禁用证书验证以解决启动问题
  ssl_ca_path: "config/ssl/123.pem"      # TiDB Cloud使用公共CA，不需要自定义证书
  ssl_cert_path: null
  ssl_key_path: null

# ==================== JWT配置 ====================
jwt:
  # JWT密钥（请生成一个复杂的随机字符串）
  # 使用命令生成: openssl rand -base64 32
  secret_key: "bouyv9F768D89F0g976f8d5f97tFD865DF97F85d78f"
  algorithm: "HS256"
  access_token_expire_minutes: 1440

# ==================== 管理员配置 ====================
# 初始管理员账户（可选）
admin:
  initial_user: "admin"
  initial_password: null  # 留空将自动生成随机密码

# ==================== 第三方API配置 ====================
# Bangumi OAuth配置
bangumi:
  client_id: "bgm4222688b7532ef439"
  client_secret: "379c426b8f26b561642334445761361f"

# 豆瓣配置
douban:
  cookie: null  # 豆瓣Cookie（可选）
