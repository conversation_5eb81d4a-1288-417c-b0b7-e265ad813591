"""
性能监控和诊断模块
实时监控数据库查询性能、内存使用和系统健康状态
"""

import asyncio
import logging
import time
import psutil
import gc
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from collections import deque, defaultdict
import aiomysql

logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        # 查询性能监控
        self._query_times = deque(maxlen=1000)  # 保留最近1000次查询
        self._slow_queries = deque(maxlen=100)   # 保留最近100次慢查询
        self._query_stats = defaultdict(list)    # 按查询类型分组的统计
        
        # 内存监控
        self._memory_snapshots = deque(maxlen=100)
        self._last_gc_time = time.time()
        
        # 系统监控
        self._system_stats = {
            'start_time': time.time(),
            'total_requests': 0,
            'error_count': 0
        }
        
        # 慢查询阈值（毫秒）
        self._slow_query_threshold = 1000
    
    def record_query(self, query_type: str, execution_time_ms: float, query_sql: str = "", success: bool = True):
        """记录查询性能"""
        timestamp = time.time()
        
        query_record = {
            'timestamp': timestamp,
            'query_type': query_type,
            'execution_time_ms': execution_time_ms,
            'success': success,
            'query_sql': query_sql[:200] if query_sql else ""  # 截断长SQL
        }
        
        self._query_times.append(query_record)
        self._query_stats[query_type].append(execution_time_ms)
        
        # 记录慢查询
        if execution_time_ms > self._slow_query_threshold:
            slow_query_record = {
                **query_record,
                'datetime': datetime.fromtimestamp(timestamp).isoformat()
            }
            self._slow_queries.append(slow_query_record)
            logger.warning(f"慢查询检测: {query_type} 耗时 {execution_time_ms:.2f}ms")
        
        # 更新系统统计
        self._system_stats['total_requests'] += 1
        if not success:
            self._system_stats['error_count'] += 1
    
    def record_memory_snapshot(self):
        """记录内存快照"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            
            snapshot = {
                'timestamp': time.time(),
                'rss_mb': memory_info.rss / 1024 / 1024,  # 物理内存
                'vms_mb': memory_info.vms / 1024 / 1024,  # 虚拟内存
                'cpu_percent': process.cpu_percent(),
                'gc_count': sum(gc.get_count())
            }
            
            self._memory_snapshots.append(snapshot)
            
            # 检查内存增长
            if len(self._memory_snapshots) >= 2:
                current = self._memory_snapshots[-1]
                previous = self._memory_snapshots[-2]
                memory_growth = current['rss_mb'] - previous['rss_mb']
                
                if memory_growth > 50:  # 内存增长超过50MB
                    logger.warning(f"内存使用快速增长: +{memory_growth:.2f}MB")
                    
                    # 触发垃圾回收
                    if time.time() - self._last_gc_time > 300:  # 5分钟内最多一次GC
                        collected = gc.collect()
                        self._last_gc_time = time.time()
                        logger.info(f"执行垃圾回收，回收了 {collected} 个对象")
        
        except Exception as e:
            logger.warning(f"内存监控失败: {e}")
    
    def get_performance_report(self) -> Dict[str, Any]:
        """生成性能报告"""
        current_time = time.time()
        uptime_hours = (current_time - self._system_stats['start_time']) / 3600
        
        # 计算查询统计
        recent_queries = [q for q in self._query_times if current_time - q['timestamp'] < 3600]  # 最近1小时
        
        if recent_queries:
            avg_response_time = sum(q['execution_time_ms'] for q in recent_queries) / len(recent_queries)
            success_rate = sum(1 for q in recent_queries if q['success']) / len(recent_queries) * 100
        else:
            avg_response_time = 0
            success_rate = 100
        
        # 按查询类型统计
        query_type_stats = {}
        for query_type, times in self._query_stats.items():
            if times:
                recent_times = [t for t in times[-100:]]  # 最近100次
                query_type_stats[query_type] = {
                    'count': len(recent_times),
                    'avg_time_ms': sum(recent_times) / len(recent_times),
                    'max_time_ms': max(recent_times),
                    'min_time_ms': min(recent_times)
                }
        
        # 内存统计
        memory_stats = {}
        if self._memory_snapshots:
            latest_memory = self._memory_snapshots[-1]
            memory_stats = {
                'current_rss_mb': latest_memory['rss_mb'],
                'current_vms_mb': latest_memory['vms_mb'],
                'cpu_percent': latest_memory['cpu_percent']
            }
            
            if len(self._memory_snapshots) >= 2:
                first_memory = self._memory_snapshots[0]
                memory_stats['memory_growth_mb'] = latest_memory['rss_mb'] - first_memory['rss_mb']
        
        return {
            'system': {
                'uptime_hours': f"{uptime_hours:.2f}",
                'total_requests': self._system_stats['total_requests'],
                'error_count': self._system_stats['error_count'],
                'error_rate': f"{(self._system_stats['error_count'] / max(1, self._system_stats['total_requests']) * 100):.2f}%"
            },
            'queries': {
                'recent_count': len(recent_queries),
                'avg_response_time_ms': f"{avg_response_time:.2f}",
                'success_rate': f"{success_rate:.2f}%",
                'slow_queries_count': len(self._slow_queries),
                'by_type': query_type_stats
            },
            'memory': memory_stats,
            'slow_queries': list(self._slow_queries)[-10:]  # 最近10个慢查询
        }
    
    async def start_monitoring(self, pool: aiomysql.Pool):
        """启动后台监控任务"""
        async def monitoring_task():
            while True:
                try:
                    # 记录内存快照
                    self.record_memory_snapshot()
                    
                    # 清理过期数据
                    await self._cleanup_expired_data()
                    
                    await asyncio.sleep(60)  # 每分钟监控一次
                except Exception as e:
                    logger.error(f"性能监控任务异常: {e}")
                    await asyncio.sleep(60)
        
        # 启动后台任务
        asyncio.create_task(monitoring_task())
        logger.info("性能监控已启动")
    
    async def _cleanup_expired_data(self):
        """清理过期的监控数据"""
        current_time = time.time()
        cutoff_time = current_time - 3600  # 保留1小时内的数据
        
        # 清理过期查询记录
        while self._query_times and self._query_times[0]['timestamp'] < cutoff_time:
            self._query_times.popleft()
        
        # 清理过期慢查询记录
        while self._slow_queries and self._slow_queries[0]['timestamp'] < cutoff_time:
            self._slow_queries.popleft()

# 全局性能监控器实例
performance_monitor = PerformanceMonitor()

def monitor_query_performance(query_type: str):
    """查询性能监控装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            success = True
            
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                raise
            finally:
                execution_time = (time.time() - start_time) * 1000
                performance_monitor.record_query(
                    query_type=query_type,
                    execution_time_ms=execution_time,
                    success=success
                )
        
        return wrapper
    return decorator
