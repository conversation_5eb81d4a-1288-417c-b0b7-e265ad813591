"""
服务降级策略管理
当数据库不可用时提供备用方案和优雅降级
"""

import json
import logging
import time
from typing import Dict, Any, List, Optional
from pathlib import Path
from datetime import datetime


class DateTimeEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理datetime对象"""
    def default(self, o):
        if isinstance(o, datetime):
            return o.isoformat()
        return super().default(o)

logger = logging.getLogger(__name__)

class ServiceDegradationManager:
    """服务降级管理器"""
    
    def __init__(self):
        self._degradation_active = False
        self._degradation_start_time = 0
        self._fallback_data_path = Path("config/fallback_data")
        self._fallback_data_path.mkdir(exist_ok=True)
        
        # 降级模式下的静态数据
        self._static_responses = {
            'search_anime': [],
            'library_anime': [],
            'popular_anime': self._get_popular_anime_fallback()
        }
    
    def _get_popular_anime_fallback(self) -> List[Dict[str, Any]]:
        """获取热门番剧的静态备用数据"""
        return [
            {
                "id": 1,
                "title": "进击的巨人",
                "type": "tv_series",
                "imageUrl": None,
                "episodeCount": 25,
                "animeId": 1,
                "animeTitle": "进击的巨人"
            },
            {
                "id": 2,
                "title": "鬼灭之刃",
                "type": "tv_series", 
                "imageUrl": None,
                "episodeCount": 12,
                "animeId": 2,
                "animeTitle": "鬼灭之刃"
            },
            {
                "id": 3,
                "title": "你的名字",
                "type": "movie",
                "imageUrl": None,
                "episodeCount": 1,
                "animeId": 3,
                "animeTitle": "你的名字"
            }
        ]
    
    def activate_degradation(self, reason: str = "数据库连接失败"):
        """激活降级模式"""
        if not self._degradation_active:
            self._degradation_active = True
            self._degradation_start_time = time.time()
            logger.error(f"服务降级已激活: {reason}")
            
            # 保存当前状态到文件
            self._save_degradation_state(reason)
    
    def deactivate_degradation(self):
        """停用降级模式"""
        if self._degradation_active:
            duration = time.time() - self._degradation_start_time
            self._degradation_active = False
            self._degradation_start_time = 0
            logger.info(f"服务降级已停用，持续时间: {duration:.2f}秒")
            
            # 清理降级状态文件
            self._clear_degradation_state()
    
    def is_degraded(self) -> bool:
        """检查是否处于降级模式"""
        return self._degradation_active
    
    def _save_degradation_state(self, reason: str):
        """保存降级状态到文件"""
        try:
            state_file = self._fallback_data_path / "degradation_state.json"
            state_data = {
                "active": True,
                "reason": reason,
                "start_time": self._degradation_start_time,
                "timestamp": datetime.now().isoformat()
            }
            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.warning(f"保存降级状态失败: {e}")
    
    def _clear_degradation_state(self):
        """清理降级状态文件"""
        try:
            state_file = self._fallback_data_path / "degradation_state.json"
            if state_file.exists():
                state_file.unlink()
        except Exception as e:
            logger.warning(f"清理降级状态失败: {e}")
    
    def get_fallback_response(self, operation_type: str, **kwargs) -> Dict[str, Any]:
        """获取降级模式下的备用响应"""
        if operation_type == "search_anime":
            return {
                "animes": [],
                "message": "数据库服务暂时不可用，请稍后重试",
                "degraded": True
            }
        
        elif operation_type == "search_episodes":
            return {
                "animes": [],
                "message": "搜索服务暂时不可用，请稍后重试", 
                "degraded": True
            }
        
        elif operation_type == "get_comments":
            return {
                "comments": [],
                "count": 0,
                "message": "弹幕服务暂时不可用",
                "degraded": True
            }
        
        elif operation_type == "library_anime":
            return {
                "animes": self._static_responses['popular_anime'],
                "message": "显示缓存数据，部分功能可能不可用",
                "degraded": True
            }
        
        else:
            return {
                "data": None,
                "message": "服务暂时不可用，请稍后重试",
                "degraded": True
            }
    
    def save_successful_response(self, operation_type: str, response_data: Any):
        """保存成功的响应数据作为备用"""
        try:
            if operation_type in self._static_responses:
                # 只保存最近的成功响应
                cache_file = self._fallback_data_path / f"{operation_type}_cache.json"
                with open(cache_file, 'w', encoding='utf-8') as f:
                    json.dump({
                        "data": response_data,
                        "cached_at": datetime.now().isoformat(),
                        "cache_ttl": 3600  # 1小时有效期
                    }, f, ensure_ascii=False, indent=2, cls=DateTimeEncoder)
        except Exception as e:
            logger.warning(f"保存备用响应数据失败: {e}")
    
    def load_cached_response(self, operation_type: str) -> Optional[Any]:
        """加载缓存的响应数据"""
        try:
            cache_file = self._fallback_data_path / f"{operation_type}_cache.json"
            if not cache_file.exists():
                return None
            
            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            # 检查缓存是否过期
            cached_time = datetime.fromisoformat(cache_data['cached_at'])
            if (datetime.now() - cached_time).total_seconds() > cache_data.get('cache_ttl', 3600):
                return None
            
            return cache_data['data']
        except Exception as e:
            logger.warning(f"加载缓存响应数据失败: {e}")
            return None
    
    def get_status(self) -> Dict[str, Any]:
        """获取降级状态"""
        status_info = {
            "degraded": self._degradation_active,
            "degradation_duration": 0,
            "fallback_data_available": {}
        }
        
        if self._degradation_active:
            status_info["degradation_duration"] = time.time() - self._degradation_start_time
        
        # 检查备用数据可用性
        for operation_type in self._static_responses.keys():
            cache_file = self._fallback_data_path / f"{operation_type}_cache.json"
            status_info["fallback_data_available"][operation_type] = cache_file.exists()
        
        return status_info

# 全局降级管理器实例
degradation_manager = ServiceDegradationManager()

def with_degradation_fallback(operation_type: str):
    """服务降级装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 检查是否处于降级模式
            if degradation_manager.is_degraded():
                logger.warning(f"服务处于降级模式，返回备用响应: {operation_type}")
                
                # 尝试加载缓存数据
                cached_data = degradation_manager.load_cached_response(operation_type)
                if cached_data:
                    return cached_data
                
                # 返回静态备用响应
                return degradation_manager.get_fallback_response(operation_type, **kwargs)
            
            try:
                result = await func(*args, **kwargs)
                
                # 保存成功的响应作为备用数据
                if result and operation_type in ["library_anime", "popular_anime"]:
                    degradation_manager.save_successful_response(operation_type, result)
                
                return result
                
            except Exception as e:
                logger.error(f"操作 {operation_type} 失败: {e}")
                
                # 激活降级模式
                degradation_manager.activate_degradation(f"{operation_type} 操作失败: {str(e)}")
                
                # 尝试返回缓存数据
                cached_data = degradation_manager.load_cached_response(operation_type)
                if cached_data:
                    logger.info(f"返回缓存数据: {operation_type}")
                    return cached_data
                
                # 返回静态备用响应
                return degradation_manager.get_fallback_response(operation_type, **kwargs)
        
        return wrapper
    return decorator
