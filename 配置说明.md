# LoveStory弹幕服务配置说明

## 📋 配置概述

本项目已简化配置管理，所有配置项都集中在 `config/config.yml` 文件中，不再使用环境变量。

## 🔧 配置文件位置

- **主配置文件**: `config/config.yml`
- **性能配置文件**: `config/performance.yml`
- **SSL证书目录**: `config/ssl/`

## ⚙️ 主要配置项

### 服务器配置
```yaml
server:
  host: "0.0.0.0"  # 服务监听地址
  port: 7768       # 服务监听端口
```

### 数据库配置
```yaml
database:
  host: "gateway01.ap-southeast-1.prod.aws.tidbcloud.com"
  port: 4000
  user: "your_username"
  password: "your_password"
  name: "your_database"
  
  # TLS/SSL安全连接配置
  ssl_enabled: true
  ssl_verify_cert: true
  ssl_ca_path: "config/ssl/123.pem"
  ssl_cert_path: null
  ssl_key_path: null
```

### JWT配置
```yaml
jwt:
  secret_key: "your_secret_key_here"  # 请使用复杂的随机字符串
  algorithm: "HS256"
  access_token_expire_minutes: 1440
```

### 管理员配置
```yaml
admin:
  initial_user: "admin"
  initial_password: null  # 留空将自动生成随机密码
```

### 第三方API配置
```yaml
# Bangumi OAuth配置
bangumi:
  client_id: "your_client_id"
  client_secret: "your_client_secret"

# 豆瓣配置
douban:
  cookie: null  # 豆瓣Cookie（可选）
```

## 🚀 配置修改步骤

1. **编辑配置文件**
   ```bash
   # 使用文本编辑器打开配置文件
   nano config/config.yml
   # 或者
   vim config/config.yml
   ```

2. **修改相应配置项**
   - 根据您的实际环境修改数据库连接信息
   - 生成并设置安全的JWT密钥
   - 配置第三方API密钥（如需要）

3. **重启服务**
   ```bash
   # 重启服务以应用新配置
   python start.py
   ```

## 🔐 安全建议

1. **JWT密钥**: 使用复杂的随机字符串，可以通过以下命令生成：
   ```bash
   openssl rand -base64 32
   ```

2. **数据库密码**: 使用强密码保护数据库

3. **SSL证书**: 生产环境建议启用SSL证书验证

4. **文件权限**: 确保配置文件的访问权限适当

## 📝 配置变更历史

- **v2.0**: 移除环境变量支持，统一使用YAML配置文件
- **v1.x**: 支持环境变量和YAML配置文件混合使用

## 🆘 故障排除

### 配置文件不存在
如果 `config/config.yml` 文件不存在，系统将使用默认配置。请复制并修改示例配置文件。

### 配置加载失败
检查YAML文件格式是否正确，确保缩进使用空格而非制表符。

### 数据库连接失败
1. 检查数据库连接信息是否正确
2. 确认网络连接正常
3. 验证SSL证书配置（如启用）

## 📞 技术支持

如遇到配置问题，请检查：
1. 配置文件语法是否正确
2. 数据库连接信息是否有效
3. 网络连接是否正常
4. 日志文件中的错误信息
