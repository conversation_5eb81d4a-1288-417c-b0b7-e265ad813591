import aiomysql
import secrets
import string
from fastapi import FastAPI, Request
from .config import settings


async def _detect_database_type(conn_config: dict) -> bool:
    """检测数据库类型，返回True表示TiDB，False表示MySQL"""
    try:
        conn = await aiomysql.connect(**conn_config)
        async with conn.cursor() as cursor:
            await cursor.execute("SELECT VERSION()")
            version_info = await cursor.fetchone()
            is_tidb = 'TiDB' in str(version_info[0]) if version_info else False
            print(f"数据库版本: {version_info[0] if version_info else 'Unknown'}")
            print(f"数据库类型: {'TiDB' if is_tidb else 'MySQL'}")
        conn.close()
        return is_tidb
    except Exception as e:
        print(f"检测数据库类型失败，默认使用MySQL配置: {e}")
        return False


async def _apply_safe_tidb_optimizations(pool: aiomysql.Pool) -> None:
    """安全地应用TiDB优化参数"""
    try:
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # 只应用最基础、最安全的TiDB优化参数
                safe_optimizations = [
                    ("tidb_max_chunk_size", "1024"),
                    ("tidb_index_lookup_size", "20000"),
                ]

                for param, value in safe_optimizations:
                    try:
                        await cursor.execute(f"SET SESSION {param} = {value}")
                        print(f"已设置TiDB参数: {param} = {value}")
                    except Exception as e:
                        print(f"设置TiDB参数 {param} 失败，跳过: {e}")

    except Exception as e:
        print(f"应用TiDB优化失败: {e}")


async def create_db_pool(app: FastAPI) -> aiomysql.Pool:
    """创建数据库连接池并存储在 app.state 中"""

    # 基础连接配置
    pool_config = {
        'host': settings.database.host,
        'port': settings.database.port,
        'user': settings.database.user,
        'password': settings.database.password,
        'db': settings.database.name,
        'autocommit': True,  # 推荐自动提交模式
        'charset': 'utf8mb4',  # 默认字符集
        'minsize': 8,        # 适中的最小连接数
        'maxsize': 32,       # 适中的最大连接数
        'pool_recycle': 3600, # 1小时回收
        'connect_timeout': 10, # 连接超时
        'echo': False,       # 生产环境关闭SQL日志
    }

    # 先创建一个临时连接来检测数据库类型
    temp_conn_config = {
        'host': settings.database.host,
        'port': settings.database.port,
        'user': settings.database.user,
        'password': settings.database.password,
        'db': settings.database.name,
        'charset': 'utf8mb4',
    }

    # 配置SSL/TLS连接
    ssl_context = None
    if settings.database.ssl_enabled:
        import ssl
        ssl_context = ssl.create_default_context()

        # 根据配置设置证书验证
        if settings.database.ssl_verify_cert:
            ssl_context.check_hostname = True
            ssl_context.verify_mode = ssl.CERT_REQUIRED
            print("已启用TLS连接，证书验证已开启")
        else:
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            print("已启用TLS连接，证书验证已关闭")

        # 设置自定义CA证书路径
        if settings.database.ssl_ca_path:
            ssl_context.load_verify_locations(cafile=settings.database.ssl_ca_path)
            print(f"已加载自定义CA证书: {settings.database.ssl_ca_path}")

        # 设置客户端证书和私钥
        if settings.database.ssl_cert_path and settings.database.ssl_key_path:
            ssl_context.load_cert_chain(
                certfile=settings.database.ssl_cert_path,
                keyfile=settings.database.ssl_key_path
            )
            print(f"已加载客户端证书: {settings.database.ssl_cert_path}")

        # 设置TLS版本
        ssl_context.minimum_version = ssl.TLSVersion.TLSv1_2
        ssl_context.maximum_version = ssl.TLSVersion.TLSv1_3
        temp_conn_config['ssl'] = ssl_context
        pool_config['ssl'] = ssl_context
        print("TLS版本设置：支持TLS 1.2和1.3")

    # 检测或获取数据库类型并应用相应配置
    if settings.database.db_type.lower() == "tidb":
        is_tidb = True
        print("手动指定TiDB数据库类型")
    elif settings.database.db_type.lower() == "mysql":
        is_tidb = False
        print("手动指定MySQL数据库类型")
    else:
        # 自动检测数据库类型
        is_tidb = await _detect_database_type(temp_conn_config)

    if is_tidb:
        print("应用TiDB兼容配置...")
        # TiDB兼容配置：不设置sql_mode和init_command，使用TiDB默认值
        # 这样可以避免SQL_MODE兼容性问题
        print("使用TiDB默认配置以确保最佳兼容性")
    else:
        print("应用MySQL兼容配置...")
        # MySQL标准配置
        pool_config['sql_mode'] = 'TRADITIONAL'
        pool_config['init_command'] = "SET SESSION innodb_lock_wait_timeout=10"

    app.state.db_pool = await aiomysql.create_pool(**pool_config)
    print(f"数据库连接池创建成功 (minsize={pool_config['minsize']}, maxsize={pool_config['maxsize']})。")
    return app.state.db_pool

async def get_db_pool(request: Request) -> aiomysql.Pool:
    """依赖项：从应用状态获取数据库连接池"""
    return request.app.state.db_pool

async def close_db_pool(app: FastAPI):
    """关闭数据库连接池"""
    if hasattr(app.state, "db_pool") and app.state.db_pool:
        app.state.db_pool.close()
        await app.state.db_pool.wait_closed()
        print("数据库连接池已关闭。")

async def create_initial_admin_user(app: FastAPI):
    """在应用启动时创建初始管理员用户（如果已配置且不存在）"""
    # 将导入移到函数内部以避免循环导入
    from . import crud
    from . import models

    admin_user = settings.admin.initial_user
    if not admin_user:
        return

    pool = app.state.db_pool
    existing_user = await crud.get_user_by_username(pool, admin_user)

    if existing_user:
        print(f"管理员用户 '{admin_user}' 已存在，跳过创建。")
        return

    # 用户不存在，开始创建
    admin_pass = settings.admin.initial_password
    if not admin_pass:
        # 生成一个安全的16位随机密码
        alphabet = string.ascii_letters + string.digits
        admin_pass = ''.join(secrets.choice(alphabet) for _ in range(16))
        print("未提供初始管理员密码，已生成随机密码。")

    user_to_create = models.UserCreate(username=admin_user, password=admin_pass)
    await crud.create_user(pool, user_to_create)

    # 打印凭据信息，方便用户查看日志
    print("\n" + "="*60)
    print(f"=== 初始管理员账户已创建 (用户: {admin_user}) ".ljust(56) + "===")
    print(f"=== 请使用以下随机生成的密码登录: {admin_pass} ".ljust(56) + "===")
    print("="*60 + "\n")

async def init_db_tables(app: FastAPI):
    """初始化数据库和表"""
    db_name = settings.database.name
    # 1. 先尝试连接MySQL实例，但不指定数据库
    try:
        connect_params = {
            'host': settings.database.host,
            'port': settings.database.port,
            'user': settings.database.user,
            'password': settings.database.password
        }

        # 配置SSL/TLS连接（TiDB Cloud支持）
        if settings.database.ssl_enabled:
            import ssl
            ssl_context = ssl.create_default_context()

            # 根据配置设置证书验证
            if settings.database.ssl_verify_cert:
                ssl_context.check_hostname = True
                ssl_context.verify_mode = ssl.CERT_REQUIRED
            else:
                ssl_context.check_hostname = False
                ssl_context.verify_mode = ssl.CERT_NONE

            # 设置自定义CA证书路径（TiDB Cloud通常使用公共CA）
            if settings.database.ssl_ca_path:
                ssl_context.load_verify_locations(cafile=settings.database.ssl_ca_path)

            # 设置客户端证书和私钥（TiDB Cloud单向认证通常不需要）
            if settings.database.ssl_cert_path and settings.database.ssl_key_path:
                ssl_context.load_cert_chain(
                    certfile=settings.database.ssl_cert_path,
                    keyfile=settings.database.ssl_key_path
                )

            # 设置TLS版本（TiDB Cloud支持TLS 1.2和1.3）
            ssl_context.minimum_version = ssl.TLSVersion.TLSv1_2
            ssl_context.maximum_version = ssl.TLSVersion.TLSv1_3
            connect_params['ssl'] = ssl_context
            print("使用TLS连接到TiDB Cloud数据库...")

        conn = await aiomysql.connect(**connect_params)
    except Exception as e:
        print(f"数据库连接失败，请检查配置: {e}")
        raise RuntimeError(f"无法连接到数据库: {e}") from e

    async with conn.cursor() as cursor:
        # 2. 创建数据库 (如果不存在)
        await cursor.execute("SHOW DATABASES LIKE %s", (db_name,))
        if not await cursor.fetchone():
            print(f"数据库 '{db_name}' 不存在，正在创建...")
            await cursor.execute(f"CREATE DATABASE `{db_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print(f"数据库 '{db_name}' 创建成功。")
    conn.close()

    # 3. 检查并创建/更新表
    async with app.state.db_pool.acquire() as conn:
        async with conn.cursor() as cursor:
            # --- 步骤 3.1: 检查并创建所有表 ---
            print("正在检查并创建数据表...")
            
            # TiDB官方兼容性优化：基于官方文档的表结构设计
            tables_to_create = {
                "anime": """CREATE TABLE `anime` (
                    `id` BIGINT NOT NULL AUTO_INCREMENT,
                    `title` VARCHAR(255) NOT NULL,
                    `type` ENUM('tv_series', 'movie', 'ova', 'other') NOT NULL DEFAULT 'tv_series',
                    `image_url` VARCHAR(512) NULL,
                    `season` INT NOT NULL DEFAULT 1,
                    `episode_count` INT NULL DEFAULT NULL,
                    `source_url` VARCHAR(512) NULL,
                    `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (`id`),
                    INDEX `idx_title` (`title`),
                    INDEX `idx_type_season` (`type`, `season`),
                    INDEX `idx_created_at` (`created_at`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;""",
                "episode": """CREATE TABLE `episode` (
                    `id` BIGINT NOT NULL AUTO_INCREMENT,
                    `source_id` BIGINT NOT NULL,
                    `title` VARCHAR(255) NOT NULL,
                    `episode_index` INT NOT NULL,
                    `provider_episode_id` VARCHAR(255) NULL,
                    `source_url` VARCHAR(512) NULL,
                    `fetched_at` TIMESTAMP NULL DEFAULT NULL,
                    `comment_count` INT NOT NULL DEFAULT 0,
                    PRIMARY KEY (`id`),
                    UNIQUE INDEX `idx_source_episode_unique` (`source_id`, `episode_index`),
                    INDEX `idx_source_id` (`source_id`),
                    INDEX `idx_episode_index` (`episode_index`),
                    INDEX `idx_comment_count` (`comment_count`),
                    INDEX `idx_fetched_at` (`fetched_at`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;""",
                "comment": """CREATE TABLE `comment` (`id` BIGINT NOT NULL AUTO_INCREMENT, `cid` VARCHAR(255) NOT NULL, `episode_id` BIGINT NOT NULL, `p` VARCHAR(255) NOT NULL, `m` TEXT NOT NULL, `t` DECIMAL(10, 2) NOT NULL, PRIMARY KEY (`id`), UNIQUE INDEX `idx_episode_cid_unique` (`episode_id` ASC, `cid` ASC), INDEX `idx_episode_time` (`episode_id` ASC, `t` ASC)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;""",
                "users": """CREATE TABLE `users` (`id` BIGINT NOT NULL AUTO_INCREMENT, `username` VARCHAR(50) NOT NULL, `hashed_password` VARCHAR(255) NOT NULL, `token` TEXT NULL, `token_update` TIMESTAMP NULL DEFAULT NULL, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, PRIMARY KEY (`id`), UNIQUE INDEX `idx_username_unique` (`username` ASC)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;""",
                "scrapers": """CREATE TABLE `scrapers` (`provider_name` VARCHAR(50) NOT NULL, `is_enabled` BOOLEAN NOT NULL DEFAULT TRUE, `display_order` INT NOT NULL DEFAULT 0, PRIMARY KEY (`provider_name`)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;""",
                "anime_sources": """CREATE TABLE `anime_sources` (`id` BIGINT NOT NULL AUTO_INCREMENT, `anime_id` BIGINT NOT NULL, `provider_name` VARCHAR(50) NOT NULL, `media_id` VARCHAR(255) NOT NULL, `is_favorited` BOOLEAN NOT NULL DEFAULT FALSE, `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP, PRIMARY KEY (`id`), UNIQUE INDEX `idx_anime_provider_media_unique` (`anime_id` ASC, `provider_name` ASC, `media_id` ASC), INDEX `idx_anime_id` (`anime_id`), INDEX `idx_provider_name` (`provider_name`), INDEX `idx_is_favorited` (`is_favorited`, `anime_id`)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;""",
                "anime_metadata": """CREATE TABLE `anime_metadata` (`id` BIGINT NOT NULL AUTO_INCREMENT, `anime_id` BIGINT NOT NULL, `tmdb_id` VARCHAR(50) NULL, `tmdb_episode_group_id` VARCHAR(50) NULL, `imdb_id` VARCHAR(50) NULL, `tvdb_id` VARCHAR(50) NULL, `douban_id` VARCHAR(50) NULL, `bangumi_id` VARCHAR(50) NULL, PRIMARY KEY (`id`), UNIQUE INDEX `idx_anime_id_unique` (`anime_id` ASC)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;""",
                "config": """CREATE TABLE `config` (`config_key` VARCHAR(100) NOT NULL, `config_value` TEXT NOT NULL, `description` TEXT NULL, PRIMARY KEY (`config_key`)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;""",
                "cache_data": """CREATE TABLE `cache_data` (`cache_provider` VARCHAR(50) NULL, `cache_key` VARCHAR(255) NOT NULL, `cache_value` LONGTEXT NOT NULL, `expires_at` TIMESTAMP NOT NULL, PRIMARY KEY (`cache_key`), INDEX `idx_expires_at` (`expires_at`)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;""",
                "api_tokens": """CREATE TABLE `api_tokens` (`id` INT NOT NULL AUTO_INCREMENT, `name` VARCHAR(100) NOT NULL, `token` VARCHAR(50) NOT NULL, `is_enabled` BOOLEAN NOT NULL DEFAULT TRUE, `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, `expires_at` TIMESTAMP NULL DEFAULT NULL, PRIMARY KEY (`id`), UNIQUE INDEX `idx_token_unique` (`token` ASC)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;""",
                "token_access_logs": """CREATE TABLE `token_access_logs` (`id` BIGINT NOT NULL AUTO_INCREMENT, `token_id` INT NOT NULL, `ip_address` VARCHAR(45) NOT NULL, `user_agent` TEXT NULL, `access_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, `status` VARCHAR(50) NOT NULL, `path` VARCHAR(512) NULL, PRIMARY KEY (`id`), INDEX `idx_token_id_time` (`token_id` ASC, `access_time` DESC)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;""",
                "ua_rules": """CREATE TABLE `ua_rules` (`id` INT NOT NULL AUTO_INCREMENT, `ua_string` VARCHAR(255) NOT NULL, `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, PRIMARY KEY (`id`), UNIQUE INDEX `idx_ua_string_unique` (`ua_string` ASC)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;""",
                "bangumi_auth": """CREATE TABLE `bangumi_auth` (`user_id` BIGINT NOT NULL, `bangumi_user_id` INT NULL, `nickname` VARCHAR(255) NULL, `avatar_url` VARCHAR(512) NULL, `access_token` TEXT NOT NULL, `refresh_token` TEXT NULL, `expires_at` TIMESTAMP NULL DEFAULT NULL, `authorized_at` TIMESTAMP NULL DEFAULT NULL, PRIMARY KEY (`user_id`)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;""",
                "oauth_states": """CREATE TABLE `oauth_states` (`state_key` VARCHAR(100) NOT NULL, `user_id` BIGINT NOT NULL, `expires_at` TIMESTAMP NOT NULL, PRIMARY KEY (`state_key`), INDEX `idx_oauth_expires_at` (`expires_at`)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;""",
                "anime_aliases": """CREATE TABLE `anime_aliases` (
                    `id` BIGINT NOT NULL AUTO_INCREMENT,
                    `anime_id` BIGINT NOT NULL,
                    `name_en` VARCHAR(255) NULL,
                    `name_jp` VARCHAR(255) NULL,
                    `name_romaji` VARCHAR(255) NULL,
                    `alias_cn_1` VARCHAR(255) NULL,
                    `alias_cn_2` VARCHAR(255) NULL,
                    `alias_cn_3` VARCHAR(255) NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE INDEX `idx_anime_id_unique` (`anime_id`),
                    INDEX `idx_name_en` (`name_en`),
                    INDEX `idx_name_jp` (`name_jp`),
                    INDEX `idx_name_romaji` (`name_romaji`),
                    INDEX `idx_alias_cn_1` (`alias_cn_1`),
                    INDEX `idx_alias_cn_2` (`alias_cn_2`),
                    INDEX `idx_alias_cn_3` (`alias_cn_3`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;""",
                "tmdb_episode_mapping": """CREATE TABLE `tmdb_episode_mapping` (`id` BIGINT NOT NULL AUTO_INCREMENT, `tmdb_tv_id` INT NOT NULL, `tmdb_episode_group_id` VARCHAR(50) NOT NULL, `tmdb_episode_id` INT NOT NULL, `tmdb_season_number` INT NOT NULL, `tmdb_episode_number` INT NOT NULL, `custom_season_number` INT NOT NULL, `custom_episode_number` INT NOT NULL, `absolute_episode_number` INT NOT NULL, PRIMARY KEY (`id`), UNIQUE KEY `idx_group_episode_unique` (`tmdb_episode_group_id`, `tmdb_episode_id`), INDEX `idx_custom_season_episode` (`tmdb_tv_id`, `tmdb_episode_group_id`, `custom_season_number`, `custom_episode_number`), INDEX `idx_absolute_episode` (`tmdb_tv_id`, `tmdb_episode_group_id`, `absolute_episode_number`)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;""",
                "scheduled_tasks": """CREATE TABLE `scheduled_tasks` (`id` VARCHAR(100) NOT NULL, `name` VARCHAR(255) NOT NULL, `job_type` VARCHAR(50) NOT NULL, `cron_expression` VARCHAR(100) NOT NULL, `is_enabled` BOOLEAN NOT NULL DEFAULT TRUE, `last_run_at` TIMESTAMP NULL DEFAULT NULL, `next_run_at` TIMESTAMP NULL DEFAULT NULL, PRIMARY KEY (`id`)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;""",
                "task_history": """CREATE TABLE `task_history` (`id` VARCHAR(100) NOT NULL, `title` VARCHAR(255) NOT NULL, `status` VARCHAR(50) NOT NULL, `progress` INT NOT NULL DEFAULT 0, `description` TEXT NULL, `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, `finished_at` TIMESTAMP NULL DEFAULT NULL, PRIMARY KEY (`id`), INDEX `idx_created_at` (`created_at` DESC)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;""",
            }

            # 先获取数据库中所有已存在的表
            await cursor.execute("SELECT table_name FROM information_schema.TABLES WHERE table_schema = %s", (db_name,))
            existing_tables = {row[0] for row in await cursor.fetchall()}

            # 遍历需要创建的表（兼容模式）
            for table_name, create_sql in tables_to_create.items():
                if table_name in existing_tables:
                    print(f"数据表 '{table_name}' 已存在，跳过创建。")
                else:
                    print(f"正在创建数据表 '{table_name}'...")
                    # 在建表语句中保留 IF NOT EXISTS 作为最后的保险
                    safe_sql = create_sql.replace(f"CREATE TABLE `{table_name}`", f"CREATE TABLE IF NOT EXISTS `{table_name}`")

                    try:
                        await cursor.execute(safe_sql)
                        print(f"数据表 '{table_name}' 创建成功。")
                    except Exception as e:
                        print(f"创建数据表 '{table_name}' 时发生错误: {e}")
                        # 对于TiDB兼容性问题，尝试简化的建表语句
                        if "FULLTEXT" in str(e) or "8214" in str(e) or "syntax" in str(e).lower():
                            print(f"检测到数据库兼容性问题，尝试简化建表语句...")
                            # 移除可能不兼容的语法
                            simplified_sql = safe_sql.replace("FULLTEXT INDEX", "INDEX")
                            simplified_sql = simplified_sql.replace("CONSTRAINT `fk_aliases_anime` FOREIGN KEY (`anime_id`) REFERENCES `anime`(`id`) ON DELETE CASCADE", "")
                            try:
                                await cursor.execute(simplified_sql)
                                print(f"数据表 '{table_name}' 使用简化语句创建成功。")
                            except Exception as e2:
                                print(f"简化建表语句仍然失败: {e2}")
                                raise e2
                        else:
                            raise e
            
            print("数据表检查完成。")

            # --- 步骤 3.2: 检查并修正旧的表结构 ---
            print("正在检查并修正表结构...")
            try:
                # 检查 token_access_logs.status
                await cursor.execute("""
                    SELECT CHARACTER_MAXIMUM_LENGTH FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_SCHEMA = %s AND TABLE_NAME = 'token_access_logs' AND COLUMN_NAME = 'status'
                """, (db_name,))
                status_col_len_row = await cursor.fetchone()
                if status_col_len_row and status_col_len_row[0] < 50:
                    print("检测到旧的 'token_access_logs.status' 列定义，正在将其更新为 VARCHAR(50)...")
                    await cursor.execute("ALTER TABLE token_access_logs MODIFY COLUMN status VARCHAR(50) NOT NULL;")
                    print("列 'token_access_logs.status' 更新成功。")

                # 检查 task_history.status
                await cursor.execute("""
                    SELECT CHARACTER_MAXIMUM_LENGTH FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_SCHEMA = %s AND TABLE_NAME = 'task_history' AND COLUMN_NAME = 'status'
                """, (db_name,))
                task_status_col_len_row = await cursor.fetchone()
                if task_status_col_len_row and task_status_col_len_row[0] < 50:
                    print("检测到旧的 'task_history.status' 列定义，正在将其更新为 VARCHAR(50)...")
                    await cursor.execute("ALTER TABLE task_history MODIFY COLUMN status VARCHAR(50) NOT NULL;")
                    print("列 'task_history.status' 更新成功。")

                # 新增：检查 config.config_value 的类型
                await cursor.execute("""
                    SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_SCHEMA = %s AND TABLE_NAME = 'config' AND COLUMN_NAME = 'config_value'
                """, (db_name,))
                config_value_col_type_row = await cursor.fetchone()
                if config_value_col_type_row and config_value_col_type_row[0].lower() not in ['text', 'longtext']:
                    print("检测到旧的 'config.config_value' 列定义，正在将其更新为 TEXT...")
                    await cursor.execute("ALTER TABLE config MODIFY COLUMN config_value TEXT NOT NULL;")
                    print("列 'config.config_value' 更新成功。")
            except Exception as e:
                # 仅记录错误，不中断启动流程
                print(f"检查或更新表结构时发生非致命错误: {e}")

            # --- 步骤 3.2: 初始化默认配置 ---
            await _init_default_config(cursor)

async def _init_default_config(cursor: aiomysql.Cursor):
    """初始化配置表的默认值，并避免打印重复键的警告。"""
    print("正在检查并初始化默认配置...")
    default_configs = [
        ('search_ttl_seconds', '300', '搜索结果的缓存时间（秒），默认5分钟。'),
        ('episodes_ttl_seconds', '1800', '分集列表的缓存时间（秒），默认30分钟。'),
        ('base_info_ttl_seconds', '1800', '基础媒体信息（如爱奇艺）的缓存时间（秒），默认30分钟。'),
        ('metadata_search_ttl_seconds', '1800', '元数据（如TMDB, Bangumi）搜索结果的缓存时间（秒），默认30分钟。'),
        ('custom_api_domain', '', '用于拼接弹幕API地址的自定义域名。'),
        ('jwt_expire_minutes', str(settings.jwt.access_token_expire_minutes), 'JWT令牌的有效期（分钟）。-1 表示永不过期。'),
        ('tmdb_api_key', '', '用于访问 The Movie Database API 的密钥。'),
        ('tmdb_api_base_url', 'https://api.themoviedb.org', 'TMDB API 的基础域名。'),
        ('tmdb_image_base_url', 'https://image.tmdb.org', 'TMDB 图片服务的基础 URL。'),
        ('ua_filter_mode', 'off', 'UA过滤模式: off, blacklist, whitelist'),
        ('douban_cookie', '', '用于访问豆瓣API的Cookie。'),
        ('webhook_api_key', '', '用于Webhook调用的安全密钥。'),
        ('tvdb_api_key', '', '用于访问 TheTVDB API 的密钥。')
    ]

    # 1. 获取所有已存在的配置键
    await cursor.execute("SELECT config_key FROM config")
    existing_keys = {row[0] for row in await cursor.fetchall()}

    # 2. 找出需要插入的新配置
    configs_to_insert = []
    for key, value, description in default_configs:
        if key in existing_keys:
            print(f"配置项 '{key}' 已存在，跳过初始化。")
        else:
            print(f"正在初始化配置项 '{key}'...")
            configs_to_insert.append((key, value, description))

    # 3. 批量插入新配置
    if configs_to_insert:
        query = "INSERT INTO config (config_key, config_value, description) VALUES (%s, %s, %s)"
        await cursor.executemany(query, configs_to_insert)
        print(f"成功初始化 {len(configs_to_insert)} 个新配置项。")
    
    print("默认配置检查完成。")
