# LoveStory弹幕服务 - 项目结构说明

## 📁 项目目录结构

```
LoveStory弹幕服务/
├── app.py                    # 宝塔部署主入口文件
├── start.py                  # 手动部署启动脚本
├── install.py                # 自动安装脚本
├── test_db_connection.py     # 数据库连接测试脚本
├── setup_ssl.py              # SSL证书配置向导
├── requirements.txt          # Python依赖包列表
├── init_database.sql         # 数据库初始化脚本
├── .env.example              # 环境变量配置示例
├── README.md                 # 项目说明文档
├── 宝塔部署说明.md           # 宝塔部署详细指南
├── 项目结构说明.md           # 本文件
├── LICENSE                   # 开源许可证
├── config/                   # 配置文件目录
│   ├── config.yml           # 主配置文件
│   ├── performance.yml      # 性能优化配置
│   ├── logs/               # 日志文件目录
│   └── ssl/                # SSL证书目录
│       ├── README.md       # SSL配置说明
│       └── ca-cert.pem     # CA证书文件（用户提供）
├── src/                     # 源代码目录
│   ├── main.py             # FastAPI应用主文件
│   ├── config.py           # 配置管理模块
│   ├── database.py         # 数据库连接管理
│   ├── models.py           # 数据模型定义
│   ├── crud.py             # 数据库操作封装
│   ├── security.py         # 安全认证模块
│   ├── dandan_api.py       # DanDanPlay兼容API
│   ├── task_manager.py     # 任务管理器
│   ├── scraper_manager.py  # 爬虫管理器
│   ├── webhook_manager.py  # Webhook管理器
│   ├── scheduler.py        # 定时任务调度器
│   ├── performance_manager.py # 性能管理器
│   ├── concurrency_controller.py # 并发控制器
│   ├── log_manager.py      # 日志管理器
│   ├── api/                # API路由模块
│   │   ├── ui.py          # Web界面API
│   │   ├── webhook_api.py # Webhook接口
│   │   ├── tmdb_api.py    # TMDB集成API
│   │   ├── tvdb_api.py    # TVDB集成API
│   │   ├── bangumi_api.py # Bangumi集成API
│   │   ├── douban_api.py  # 豆瓣集成API
│   │   └── imdb_api.py    # IMDb集成API
│   ├── scrapers/           # 弹幕爬虫模块
│   │   ├── base.py        # 爬虫基类
│   │   ├── bilibili.py    # B站弹幕爬虫
│   │   ├── tencent.py     # 腾讯视频爬虫
│   │   ├── iqiyi.py       # 爱奇艺爬虫
│   │   ├── youku.py       # 优酷爬虫
│   │   └── gamer.py       # 巴哈姆特爬虫
│   ├── webhook/            # Webhook处理模块
│   │   ├── base.py        # Webhook基类
│   │   ├── emby.py        # Emby集成
│   │   └── tasks.py       # Webhook任务处理
│   └── jobs/               # 后台任务模块
│       ├── base.py        # 任务基类
│       └── tmdb_auto_map.py # TMDB自动映射任务
└── static/                 # 前端静态文件
    ├── index.html         # 主页面
    ├── logo.png           # 项目Logo
    ├── placeholder.png    # 占位图片
    ├── css/               # 样式文件
    │   ├── base.css       # 基础样式
    │   ├── components.css # 组件样式
    │   └── views/         # 页面样式
    └── js/                # JavaScript文件
        ├── main.js        # 主脚本
        ├── api.js         # API调用
        ├── auth.js        # 认证相关
        ├── ui.js          # UI交互
        └── views/         # 页面脚本
```

## 🔧 核心文件说明

### 入口文件
- **`app.py`**: 宝塔面板部署的主入口文件，导出application对象
- **`start.py`**: 手动部署的启动脚本，包含启动信息显示
- **`install.py`**: 自动安装脚本，检查环境并安装依赖
- **`test_db_connection.py`**: 数据库连接测试工具，支持TLS连接验证
- **`setup_ssl.py`**: SSL证书配置向导，帮助配置远程数据库TLS连接

### 配置文件
- **`config/config.yml`**: 主配置文件，包含服务器、数据库、JWT等所有配置
- **`config/performance.yml`**: 性能优化配置，包含并发控制、缓存等设置
- **`config/ssl/`**: SSL证书目录，存放远程数据库TLS连接所需的证书文件

### 核心模块
- **`src/main.py`**: FastAPI应用初始化，路由注册，生命周期管理
- **`src/config.py`**: 配置文件读取和管理
- **`src/database.py`**: 数据库连接池管理和表结构初始化
- **`src/models.py`**: Pydantic数据模型定义
- **`src/crud.py`**: 数据库CRUD操作封装

### 业务逻辑
- **`src/task_manager.py`**: 异步任务队列管理，支持并发控制
- **`src/scraper_manager.py`**: 多源爬虫协调和生命周期管理
- **`src/webhook_manager.py`**: Webhook事件处理和任务创建
- **`src/scheduler.py`**: 定时任务调度，如缓存清理等
- **`src/performance_manager.py`**: 性能监控和自动优化

### API模块
- **`src/api/ui.py`**: Web管理界面的后端API
- **`src/dandan_api.py`**: 兼容DanDanPlay的弹幕API
- **`src/api/webhook_api.py`**: 接收外部系统通知的Webhook接口
- **其他API文件**: 第三方服务集成（TMDB、TVDB等）

### 爬虫模块
- **`src/scrapers/base.py`**: 爬虫基类，定义通用接口和工具函数
- **各平台爬虫**: 实现具体平台的弹幕获取逻辑

### 前端文件
- **`static/index.html`**: 单页面应用主页
- **`static/css/`**: 样式文件，采用模块化设计
- **`static/js/`**: JavaScript文件，包含API调用和UI交互

## 🚀 部署相关文件

### 宝塔部署
- **`app.py`**: 宝塔Python项目管理器的入口文件
- **`宝塔部署说明.md`**: 详细的宝塔部署指南
- **`init_database.sql`**: 数据库初始化SQL脚本

### 手动部署
- **`start.py`**: 手动启动脚本
- **`install.py`**: 环境检查和依赖安装脚本
- **`requirements.txt`**: Python依赖包列表

## 📝 配置说明

### 必须配置的文件
1. **`config/config.yml`** - 修改数据库连接信息和JWT密钥
2. **数据库** - 创建数据库和用户（使用init_database.sql）

### 可选配置的文件
1. **`config/performance.yml`** - 性能优化参数（使用默认值即可）

## 🔄 启动流程

1. **环境检查**: 运行 `python install.py` 检查环境
2. **数据库配置**: 修改配置文件并创建数据库
3. **启动服务**: 
   - 宝塔: 在Python项目管理器中启动
   - 手动: 运行 `python start.py` 或 `python app.py`

## 📊 监控和日志

- **日志文件**: 存储在 `config/logs/` 目录
- **性能监控**: 通过Web界面查看系统状态
- **任务状态**: 在Web界面的任务管理页面查看

---

**注意**: 部署前请仔细阅读 `宝塔部署说明.md` 文件，确保正确配置所有必要参数。
