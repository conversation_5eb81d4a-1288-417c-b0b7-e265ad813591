# TiDB兼容性修复说明

## 🔍 问题分析

### 原始问题
TiDB不支持某些MySQL的SQL_MODE参数，导致连接时出现语法错误：
```
pymysql.err.ProgrammingError: (1064, 'You have an error in your SQL syntax; check the manual that corresponds to your TiDB version for the right syntax to use line 1 column 38 near ",NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO"
```

### 根本原因
1. **SQL_MODE不兼容**：TiDB不支持MySQL的某些SQL_MODE值
2. **硬编码配置**：代码中硬编码了MySQL特定的配置
3. **缺乏数据库类型检测**：没有根据数据库类型应用不同配置

## 🔧 修复方案

### 1. 添加数据库类型配置
在 `src/config.py` 中添加了 `db_type` 配置项：
```python
class DatabaseConfig(BaseModel):
    # ...
    db_type: str = "auto"  # auto, mysql, tidb
```

### 2. 实现数据库类型检测
在 `src/database.py` 中添加了自动检测功能：
```python
async def _detect_database_type(conn_config: dict) -> bool:
    """检测数据库类型，返回True表示TiDB，False表示MySQL"""
    # 通过VERSION()函数检测是否包含'TiDB'字符串
```

### 3. 条件化配置应用
根据数据库类型应用不同的配置：

#### TiDB配置（兼容模式）
- **不设置sql_mode**：使用TiDB默认值避免兼容性问题
- **不设置init_command**：避免不支持的参数
- **使用默认配置**：确保最佳兼容性

#### MySQL配置（传统模式）
- **sql_mode**: `TRADITIONAL`
- **init_command**: `SET SESSION innodb_lock_wait_timeout=10`

### 4. 更新配置文件
在 `config/config.yml` 中添加了数据库类型配置：
```yaml
database:
  # 数据库类型配置（重要：解决TiDB兼容性问题）
  # 选项: auto（自动检测）, mysql（MySQL/MariaDB）, tidb（TiDB）
  db_type: "tidb"  # 手动指定为TiDB以避免兼容性问题
```

## 📋 修复的文件列表

1. **src/config.py** - 添加数据库类型配置
2. **src/database.py** - 实现数据库类型检测和条件化配置
3. **config/config.yml** - 更新配置文件示例
4. **test_tidb_compatibility.py** - 创建兼容性测试脚本

## 🚀 使用方法

### 方法1：手动指定数据库类型（推荐）
在 `config/config.yml` 中设置：
```yaml
database:
  db_type: "tidb"  # 明确指定TiDB
```

### 方法2：自动检测
在 `config/config.yml` 中设置：
```yaml
database:
  db_type: "auto"  # 自动检测数据库类型
```

## 🧪 测试验证

运行兼容性测试：
```bash
python test_tidb_compatibility.py
```

测试内容包括：
1. 数据库连接测试
2. 数据库类型检测
3. SQL_MODE兼容性测试
4. TiDB特定参数测试
5. 基础SQL操作测试
6. 连接池创建测试

## ⚡ 性能影响

### 最小化性能影响
1. **数据库类型检测**：只在启动时执行一次
2. **移除不兼容参数**：避免错误重试，提高启动速度
3. **保留核心功能**：不影响业务逻辑

### TiDB优化保留
- TiDB优化器 (`src/tidb_optimizer.py`) 保持不变
- 可以在应用启动后手动应用优化参数
- 错误处理确保不会因优化失败而影响启动

## 🔒 安全性考虑

1. **向后兼容**：MySQL数据库仍然使用原有配置
2. **错误处理**：所有数据库操作都有适当的错误处理
3. **默认安全**：TiDB使用默认配置，避免不安全的自定义设置

## 📝 配置建议

### 生产环境
```yaml
database:
  db_type: "tidb"  # 明确指定，避免检测开销
  ssl_enabled: true
  ssl_verify_cert: false  # TiDB Cloud通常使用此设置
```

### 开发环境
```yaml
database:
  db_type: "auto"  # 自动检测，方便切换数据库
  ssl_enabled: false
```

## 🎯 预期效果

修复后应该能够：
1. ✅ 成功连接TiDB数据库
2. ✅ 避免SQL_MODE兼容性错误
3. ✅ 保持MySQL数据库的正常功能
4. ✅ 不影响应用性能
5. ✅ 支持手动和自动数据库类型检测

## 🔄 回滚方案

如果修复出现问题，可以：
1. 将 `db_type` 设置为 `"mysql"`
2. 或者恢复到修复前的代码版本
3. 检查数据库连接配置是否正确
