"""
TiDB性能优化器
基于TiDB官方文档的性能优化最佳实践
"""

import logging
import aiomysql
from typing import Dict, Any, List

logger = logging.getLogger(__name__)

class TiDBOptimizer:
    """TiDB性能优化器"""
    
    def __init__(self):
        self.optimization_applied = False
        self.supported_features = {
            'parallel_apply': True,
            'chunk_optimization': True,
            'index_lookup_optimization': True,
            'hash_join_optimization': True,
            'projection_optimization': True,
            'executor_optimization': True
        }
    
    async def apply_session_optimizations(self, pool: aiomysql.Pool) -> bool:
        """应用TiDB会话级优化"""
        try:
            async with pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    # TiDB官方推荐的性能优化参数
                    optimizations = [
                        # 启用并行应用优化
                        "SET SESSION tidb_enable_parallel_apply = ON",
                        
                        # 优化数据块大小
                        "SET SESSION tidb_max_chunk_size = 1024",
                        
                        # 索引查找优化
                        "SET SESSION tidb_index_lookup_size = 20000",
                        "SET SESSION tidb_index_lookup_concurrency = 4",
                        
                        # Hash Join优化
                        "SET SESSION tidb_hash_join_concurrency = 5",
                        
                        # 投影优化
                        "SET SESSION tidb_projection_concurrency = 4",
                        
                        # 执行器并发优化
                        "SET SESSION tidb_executor_concurrency = 5",
                        
                        # 禁用代价模型版本2（提高稳定性）
                        "SET SESSION tidb_cost_model_version = 1",
                        
                        # 启用索引合并
                        "SET SESSION tidb_enable_index_merge = ON",
                        
                        # 优化统计信息使用
                        "SET SESSION tidb_analyze_version = 2"
                    ]
                    
                    for optimization in optimizations:
                        try:
                            await cursor.execute(optimization)
                            logger.debug(f"应用优化: {optimization}")
                        except Exception as e:
                            logger.warning(f"优化参数设置失败: {optimization}, 错误: {e}")
                    
                    self.optimization_applied = True
                    logger.info("TiDB会话优化已应用")
                    return True
                    
        except Exception as e:
            logger.error(f"应用TiDB优化失败: {e}")
            return False
    
    async def check_tidb_version(self, pool: aiomysql.Pool) -> Dict[str, Any]:
        """检查TiDB版本和功能支持"""
        try:
            async with pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 获取TiDB版本信息
                    await cursor.execute("SELECT VERSION() as version")
                    version_info = await cursor.fetchone()
                    
                    # 检查是否为TiDB
                    is_tidb = 'TiDB' in version_info['version']
                    
                    # 获取TiDB特定信息
                    tidb_info = {
                        'version': version_info['version'],
                        'is_tidb': is_tidb,
                        'supported_features': self.supported_features if is_tidb else {}
                    }
                    
                    if is_tidb:
                        # 获取TiDB特定的系统变量
                        try:
                            await cursor.execute("SHOW VARIABLES LIKE 'tidb_version'")
                            tidb_version = await cursor.fetchone()
                            if tidb_version:
                                tidb_info['tidb_version'] = tidb_version['Value']
                        except:
                            pass
                    
                    logger.info(f"数据库信息: {tidb_info}")
                    return tidb_info
                    
        except Exception as e:
            logger.error(f"检查TiDB版本失败: {e}")
            return {'version': 'unknown', 'is_tidb': False, 'supported_features': {}}
    
    async def optimize_table_statistics(self, pool: aiomysql.Pool, table_names: List[str]) -> bool:
        """优化表统计信息"""
        try:
            async with pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    for table_name in table_names:
                        try:
                            # TiDB推荐的统计信息收集
                            await cursor.execute(f"ANALYZE TABLE {table_name}")
                            logger.info(f"已更新表 {table_name} 的统计信息")
                        except Exception as e:
                            logger.warning(f"更新表 {table_name} 统计信息失败: {e}")
                    
                    return True
                    
        except Exception as e:
            logger.error(f"优化表统计信息失败: {e}")
            return False
    
    async def get_performance_insights(self, pool: aiomysql.Pool) -> Dict[str, Any]:
        """获取TiDB性能洞察"""
        insights = {
            'slow_queries': [],
            'table_stats': {},
            'index_usage': {},
            'recommendations': []
        }
        
        try:
            async with pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    # 获取慢查询信息（如果支持）
                    try:
                        await cursor.execute("""
                            SELECT query_time, query, rows_examined, rows_sent
                            FROM information_schema.slow_query
                            WHERE time > DATE_SUB(NOW(), INTERVAL 1 HOUR)
                            ORDER BY query_time DESC
                            LIMIT 10
                        """)
                        insights['slow_queries'] = await cursor.fetchall()
                    except:
                        # TiDB可能不支持slow_query表
                        pass
                    
                    # 获取表大小信息
                    try:
                        await cursor.execute("""
                            SELECT table_name, table_rows, data_length, index_length
                            FROM information_schema.tables
                            WHERE table_schema = DATABASE()
                            AND table_type = 'BASE TABLE'
                        """)
                        table_stats = await cursor.fetchall()
                        insights['table_stats'] = {row['table_name']: row for row in table_stats}
                    except Exception as e:
                        logger.warning(f"获取表统计信息失败: {e}")
                    
                    # 生成优化建议
                    insights['recommendations'] = self._generate_recommendations(insights)
                    
        except Exception as e:
            logger.error(f"获取性能洞察失败: {e}")
        
        return insights
    
    def _generate_recommendations(self, insights: Dict[str, Any]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 基于表大小的建议
        for table_name, stats in insights.get('table_stats', {}).items():
            if stats.get('table_rows', 0) > 100000:
                recommendations.append(f"表 {table_name} 数据量较大({stats['table_rows']}行)，建议定期更新统计信息")
            
            if stats.get('index_length', 0) > stats.get('data_length', 0) * 2:
                recommendations.append(f"表 {table_name} 索引大小过大，建议检查索引使用情况")
        
        # 基于慢查询的建议
        if len(insights.get('slow_queries', [])) > 0:
            recommendations.append("检测到慢查询，建议优化查询语句或添加适当索引")
        
        # 通用TiDB优化建议
        if not self.optimization_applied:
            recommendations.append("建议应用TiDB会话级优化参数")
        
        return recommendations

# 全局TiDB优化器实例
tidb_optimizer = TiDBOptimizer()

async def initialize_tidb_optimizations(pool: aiomysql.Pool) -> Dict[str, Any]:
    """初始化TiDB优化"""
    logger.info("开始初始化TiDB性能优化...")
    
    # 检查TiDB版本
    db_info = await tidb_optimizer.check_tidb_version(pool)
    
    if db_info['is_tidb']:
        # 应用TiDB优化
        optimization_success = await tidb_optimizer.apply_session_optimizations(pool)
        
        # 优化核心表的统计信息
        core_tables = ['anime', 'anime_aliases', 'episode', 'comment', 'anime_sources']
        stats_success = await tidb_optimizer.optimize_table_statistics(pool, core_tables)
        
        logger.info(f"TiDB优化完成 - 会话优化: {optimization_success}, 统计信息优化: {stats_success}")
    else:
        logger.info("检测到非TiDB数据库，跳过TiDB特定优化")
    
    return {
        'database_info': db_info,
        'optimizations_applied': db_info['is_tidb'],
        'timestamp': logger.info.__defaults__[0] if hasattr(logger.info, '__defaults__') else 'unknown'
    }
