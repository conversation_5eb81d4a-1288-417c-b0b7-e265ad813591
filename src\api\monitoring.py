"""
系统监控API端点
提供性能监控、健康检查和诊断信息
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status
from typing import Dict, Any
import aiomysql

from ..database import get_db_pool
from ..cache_manager import performance_cache, get_cache_stats
from ..db_health import db_health_manager
from ..performance_monitor import performance_monitor
from ..service_degradation import degradation_manager
# from .. import security  # 暂时注释掉，避免导入错误

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/monitoring", tags=["监控"])

@router.get("/health", summary="系统健康检查")
async def health_check(
    pool: aiomysql.Pool = Depends(get_db_pool)
) -> Dict[str, Any]:
    """系统健康检查端点"""
    try:
        # 数据库健康检查
        db_healthy = await db_health_manager.check_pool_health(pool)
        
        # 获取各组件状态
        db_status = db_health_manager.get_health_status()
        cache_stats = await get_cache_stats(pool) if db_healthy else {}
        degradation_status = degradation_manager.get_status()
        
        overall_status = "healthy"
        if degradation_status["degraded"]:
            overall_status = "degraded"
        elif not db_healthy:
            overall_status = "unhealthy"
        elif db_status["status"] != "healthy":
            overall_status = "warning"
        
        return {
            "status": overall_status,
            "timestamp": performance_monitor._system_stats.get('start_time', 0),
            "database": {
                "healthy": db_healthy,
                "details": db_status
            },
            "cache": cache_stats,
            "degradation": degradation_status,
            "uptime_seconds": performance_monitor._system_stats.get('start_time', 0)
        }
    
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "error",
            "message": str(e),
            "timestamp": performance_monitor._system_stats.get('start_time', 0)
        }

@router.get("/performance", summary="性能报告")
async def performance_report(
    # current_user = Depends(security.get_current_user),  # 暂时注释掉权限检查
    pool: aiomysql.Pool = Depends(get_db_pool)
) -> Dict[str, Any]:
    """获取详细的性能报告（需要管理员权限）"""
    try:
        # 获取性能报告
        perf_report = performance_monitor.get_performance_report()
        
        # 获取缓存统计
        cache_stats = await get_cache_stats(pool)
        
        # 获取数据库健康状态
        db_health = db_health_manager.get_health_status()
        
        return {
            "performance": perf_report,
            "cache": cache_stats,
            "database_health": db_health,
            "generated_at": performance_monitor._system_stats.get('start_time', 0)
        }
    
    except Exception as e:
        logger.error(f"获取性能报告失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取性能报告失败: {str(e)}"
        )

@router.post("/cache/clear", summary="清理缓存")
async def clear_cache(
    cache_type: str = "all",  # all, memory, database
    # current_user = Depends(security.get_current_user),  # 暂时注释掉权限检查
    pool: aiomysql.Pool = Depends(get_db_pool)
) -> Dict[str, Any]:
    """清理缓存（需要管理员权限）"""
    try:
        cleared_items = 0
        
        if cache_type in ["all", "memory"]:
            # 清理内存缓存
            memory_items = len(performance_cache._memory_cache)
            performance_cache._memory_cache.clear()
            performance_cache._memory_access_times.clear()
            cleared_items += memory_items
            logger.info(f"已清理 {memory_items} 个内存缓存项")
        
        if cache_type in ["all", "database"]:
            # 清理数据库缓存
            async with pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("DELETE FROM cache_data")
                    db_items = cursor.rowcount
                    cleared_items += db_items
                    logger.info(f"已清理 {db_items} 个数据库缓存项")
        
        return {
            "success": True,
            "cleared_items": cleared_items,
            "cache_type": cache_type,
            "message": f"已清理 {cleared_items} 个缓存项"
        }
    
    except Exception as e:
        logger.error(f"清理缓存失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清理缓存失败: {str(e)}"
        )

@router.post("/degradation/toggle", summary="切换降级模式")
async def toggle_degradation(
    enable: bool,
    reason: str = "手动切换",
    # current_user = Depends(security.get_current_user),  # 暂时注释掉权限检查
) -> Dict[str, Any]:
    """手动切换服务降级模式（需要管理员权限）"""
    try:
        if enable:
            degradation_manager.activate_degradation(f"手动激活: {reason}")
            message = "服务降级模式已激活"
        else:
            degradation_manager.deactivate_degradation()
            message = "服务降级模式已停用"
        
        return {
            "success": True,
            "degraded": degradation_manager.is_degraded(),
            "message": message
        }
    
    except Exception as e:
        logger.error(f"切换降级模式失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"切换降级模式失败: {str(e)}"
        )

@router.get("/metrics", summary="系统指标")
async def system_metrics() -> Dict[str, Any]:
    """获取系统指标（公开端点，用于监控系统）"""
    try:
        # 基础指标
        basic_metrics = {
            "uptime": performance_monitor._system_stats.get('start_time', 0),
            "total_requests": performance_monitor._system_stats.get('total_requests', 0),
            "error_count": performance_monitor._system_stats.get('error_count', 0),
            "degraded": degradation_manager.is_degraded()
        }
        
        # 计算错误率
        total_requests = basic_metrics["total_requests"]
        error_rate = 0 if total_requests == 0 else (basic_metrics["error_count"] / total_requests * 100)
        basic_metrics["error_rate"] = f"{error_rate:.2f}%"
        
        return basic_metrics
    
    except Exception as e:
        logger.error(f"获取系统指标失败: {e}")
        return {
            "error": str(e),
            "timestamp": performance_monitor._system_stats.get('start_time', 0)
        }
