# TiDB Cloud 连接配置指南

## 📋 概述

本指南将帮助您配置LoveStory弹幕服务以连接到TiDB Cloud数据库。TiDB Cloud是一个完全托管的云原生数据库服务，提供高性能、高可用性和强一致性。

## 🔧 配置步骤

### 1. 获取TiDB Cloud连接信息

1. **登录TiDB Cloud控制台**
   - 访问 [TiDB Cloud](https://tidbcloud.com/)
   - 使用您的账户登录

2. **选择您的集群**
   - 在控制台中选择要连接的TiDB集群
   - 确保集群状态为"Available"

3. **获取连接信息**
   - 点击集群的"Connect"按钮
   - 选择"General"连接方式
   - 复制以下信息：
     - Host（主机地址）
     - Port（端口，通常为4000）
     - Username（用户名）
     - Password（密码）
     - Database Name（数据库名称）

### 2. 配置数据库连接

编辑 `config/config.yml` 文件：

```yaml
# TiDB Cloud数据库配置
database:
  host: "gateway01.ap-southeast-1.prod.aws.tidbcloud.com"  # 替换为您的TiDB Cloud网关地址
  port: 4000  # TiDB Cloud默认端口
  user: "your_tidb_username"  # 您的TiDB Cloud用户名
  password: "your_tidb_password"  # 您的TiDB Cloud密码
  name: "your_database_name"  # 数据库名称

  # TLS安全连接配置（TiDB Cloud必需）
  ssl_enabled: true  # 必须启用SSL/TLS连接
  ssl_verify_cert: true  # 推荐启用证书验证
  ssl_ca_path: null  # TiDB Cloud使用公共CA，不需要自定义CA证书
  ssl_cert_path: null  # TiDB Cloud单向认证，不需要客户端证书
  ssl_key_path: null  # TiDB Cloud单向认证，不需要客户端私钥
```

### 3. TLS连接说明

**TiDB Cloud TLS特性：**
- ✅ 支持TLS 1.2和TLS 1.3
- ✅ 使用公共CA证书（Let's Encrypt等）
- ✅ 支持单向TLS认证（推荐）
- ✅ 支持双向TLS认证（可选）
- ✅ 强制加密连接

**配置要点：**
- `ssl_enabled` 必须设置为 `true`
- `ssl_verify_cert` 推荐设置为 `true`
- 通常不需要自定义CA证书文件
- 通常不需要客户端证书文件

### 4. 测试连接

运行连接测试脚本：

```bash
python test_db_connection.py
```

成功的输出示例：
```
🔍 TiDB Cloud数据库连接测试
============================================================
📍 数据库地址: gateway01.ap-southeast-1.prod.aws.tidbcloud.com:4000
👤 用户名: your_username
🗄️  数据库: your_database
🔐 SSL启用: True
🔒 证书验证: True
📜 使用系统默认CA证书（TiDB Cloud标准配置）
📄 未配置客户端证书（TiDB Cloud单向认证）
🔐 TLS版本：1.2-1.3（TiDB Cloud兼容）
🔌 正在连接数据库...
✅ 数据库连接成功！
📊 数据库版本: 5.7.25-TiDB-v6.5.0
🔐 SSL加密套件: TLS_AES_256_GCM_SHA384
✅ TLS连接已建立并正常工作
```

## 🚀 启动应用

连接测试成功后，您可以启动应用：

```bash
# 宝塔部署
# 在宝塔面板中配置Python项目，入口文件为 app.py

# 手动部署
python start.py
```

## 🔍 故障排除

### 常见问题

1. **连接超时**
   - 检查网络连接
   - 确认TiDB Cloud集群状态为"Available"
   - 检查防火墙设置

2. **SSL连接失败**
   - 确保 `ssl_enabled: true`
   - 检查TLS版本兼容性
   - 验证证书配置

3. **认证失败**
   - 检查用户名和密码
   - 确认用户权限
   - 检查数据库名称

4. **权限不足**
   - 确保用户具有数据库访问权限
   - 检查IP白名单设置

### 调试技巧

1. **启用详细日志**
   ```python
   # 在 src/database.py 中临时启用
   pool_config['echo'] = True
   ```

2. **检查SSL状态**
   ```sql
   SELECT @@ssl_cipher;
   SHOW STATUS LIKE 'Ssl%';
   ```

3. **验证连接参数**
   ```python
   # 在测试脚本中添加调试信息
   print(f"连接参数: {connect_params}")
   ```

## 📚 相关文档

- [TiDB Cloud官方文档](https://docs.pingcap.com/tidbcloud/)
- [TiDB Cloud连接指南](https://docs.pingcap.com/tidbcloud/connect-to-tidb-cluster)
- [TLS连接配置](https://docs.pingcap.com/tidbcloud/secure-connections-to-serverless-tier-clusters)

## 💡 最佳实践

1. **安全性**
   - 始终启用TLS连接
   - 使用强密码
   - 定期轮换密码
   - 限制IP访问

2. **性能优化**
   - 合理配置连接池大小
   - 启用连接复用
   - 监控连接状态

3. **监控**
   - 监控连接数
   - 监控查询性能
   - 设置告警规则

## 🎯 下一步

配置完成后，您可以：
1. 运行数据库初始化脚本
2. 创建管理员账户
3. 启动Web服务
4. 配置反向代理（如Nginx）
5. 设置监控和日志
