"""
数据库健康检查和连接管理
针对TiDB Cloud优化的连接稳定性保障
"""

import asyncio
import logging
import time
from typing import Optional, Dict, Any
import aiomysql
from fastapi import HTTPException, status

logger = logging.getLogger(__name__)

class DatabaseHealthManager:
    """数据库健康检查和连接管理器"""
    
    def __init__(self):
        self._last_health_check = 0
        self._health_check_interval = 30  # 30秒检查一次
        self._connection_failures = 0
        self._max_failures = 3
        self._circuit_breaker_timeout = 60  # 熔断器超时时间
        self._circuit_breaker_open_time = 0
        self._performance_metrics = {
            'total_queries': 0,
            'failed_queries': 0,
            'avg_response_time': 0,
            'slow_queries': 0
        }
    
    async def check_pool_health(self, pool: aiomysql.Pool) -> bool:
        """检查连接池健康状态"""
        try:
            async with pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    start_time = time.time()
                    await cursor.execute("SELECT 1")
                    response_time = (time.time() - start_time) * 1000
                    
                    # 更新性能指标
                    self._update_performance_metrics(response_time, success=True)
                    
                    if response_time > 1000:  # 超过1秒认为是慢查询
                        logger.warning(f"数据库响应较慢: {response_time:.2f}ms")
                    
                    self._connection_failures = 0
                    return True
        except Exception as e:
            self._connection_failures += 1
            self._update_performance_metrics(0, success=False)
            logger.error(f"数据库健康检查失败 (失败次数: {self._connection_failures}): {e}")
            return False
    
    def _update_performance_metrics(self, response_time: float, success: bool):
        """更新性能指标"""
        self._performance_metrics['total_queries'] += 1
        
        if success:
            # 更新平均响应时间
            current_avg = self._performance_metrics['avg_response_time']
            total_queries = self._performance_metrics['total_queries']
            self._performance_metrics['avg_response_time'] = (
                (current_avg * (total_queries - 1) + response_time) / total_queries
            )
            
            if response_time > 1000:
                self._performance_metrics['slow_queries'] += 1
        else:
            self._performance_metrics['failed_queries'] += 1
    
    def is_circuit_breaker_open(self) -> bool:
        """检查熔断器是否开启"""
        if self._connection_failures >= self._max_failures:
            if time.time() - self._circuit_breaker_open_time < self._circuit_breaker_timeout:
                return True
            else:
                # 熔断器超时，重置状态
                self._connection_failures = 0
                self._circuit_breaker_open_time = 0
                return False
        return False
    
    def open_circuit_breaker(self):
        """开启熔断器"""
        self._circuit_breaker_open_time = time.time()
        logger.error("数据库连接熔断器已开启，暂停数据库操作")
    
    async def execute_with_retry(self, pool: aiomysql.Pool, operation, max_retries: int = 3):
        """带重试机制的数据库操作执行"""
        if self.is_circuit_breaker_open():
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="数据库服务暂时不可用，请稍后重试"
            )
        
        last_exception = None
        for attempt in range(max_retries):
            try:
                start_time = time.time()
                result = await operation()
                response_time = (time.time() - start_time) * 1000
                
                # 更新性能指标
                self._update_performance_metrics(response_time, success=True)
                
                # 重置失败计数
                if self._connection_failures > 0:
                    self._connection_failures = max(0, self._connection_failures - 1)
                
                return result
                
            except (aiomysql.Error, OSError, asyncio.TimeoutError) as e:
                last_exception = e
                self._connection_failures += 1
                self._update_performance_metrics(0, success=False)
                
                logger.warning(f"数据库操作失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                
                if attempt < max_retries - 1:
                    # 指数退避
                    wait_time = (2 ** attempt) * 0.1
                    await asyncio.sleep(wait_time)
                    
                    # 检查连接池健康状态
                    if not await self.check_pool_health(pool):
                        logger.error("连接池健康检查失败，跳过重试")
                        break
        
        # 所有重试都失败了
        if self._connection_failures >= self._max_failures:
            self.open_circuit_breaker()
        
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"数据库操作失败: {last_exception}"
        )
    
    async def periodic_health_check(self, pool: aiomysql.Pool):
        """定期健康检查任务"""
        while True:
            try:
                current_time = time.time()
                if current_time - self._last_health_check >= self._health_check_interval:
                    await self.check_pool_health(pool)
                    self._last_health_check = current_time
                
                await asyncio.sleep(10)  # 每10秒检查一次是否需要健康检查
            except Exception as e:
                logger.error(f"定期健康检查任务异常: {e}")
                await asyncio.sleep(30)  # 异常时等待30秒再继续
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态报告"""
        total_queries = self._performance_metrics['total_queries']
        success_rate = 0 if total_queries == 0 else (
            (total_queries - self._performance_metrics['failed_queries']) / total_queries * 100
        )
        
        return {
            'connection_failures': self._connection_failures,
            'circuit_breaker_open': self.is_circuit_breaker_open(),
            'performance_metrics': self._performance_metrics,
            'success_rate': f"{success_rate:.2f}%",
            'last_health_check': self._last_health_check,
            'status': 'healthy' if self._connection_failures == 0 else 'degraded' if self._connection_failures < self._max_failures else 'unhealthy'
        }

# 全局健康管理器实例
db_health_manager = DatabaseHealthManager()

async def safe_db_operation(pool: aiomysql.Pool, operation):
    """安全的数据库操作包装器"""
    return await db_health_manager.execute_with_retry(pool, operation)

def db_operation_timeout(timeout_seconds: int = 30):
    """数据库操作超时装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                return await asyncio.wait_for(func(*args, **kwargs), timeout=timeout_seconds)
            except asyncio.TimeoutError:
                logger.error(f"数据库操作超时 ({timeout_seconds}s): {func.__name__}")
                raise HTTPException(
                    status_code=status.HTTP_504_GATEWAY_TIMEOUT,
                    detail=f"数据库操作超时"
                )
        return wrapper
    return decorator

async def ensure_pool_ready(pool: aiomysql.Pool) -> bool:
    """确保连接池就绪"""
    try:
        if pool.closed:
            logger.error("连接池已关闭")
            return False
        
        # 检查可用连接
        if pool.size == 0:
            logger.warning("连接池中没有可用连接")
            return False
        
        return await db_health_manager.check_pool_health(pool)
    except Exception as e:
        logger.error(f"连接池状态检查失败: {e}")
        return False
