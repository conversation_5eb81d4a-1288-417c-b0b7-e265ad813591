-- LoveStory弹幕服务 TiDB Cloud数据库初始化脚本
-- 适用于TiDB Cloud部署

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS danmu_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE danmu_db;

-- 创建用户（TiDB Cloud通常已经提供用户，此步骤可选）
-- CREATE USER IF NOT EXISTS 'danmu_user'@'%' IDENTIFIED BY 'your_strong_password';
-- GRANT ALL PRIVILEGES ON danmu_db.* TO 'danmu_user'@'%';

-- 创建主要数据表（TiDB兼容版本）

-- 1. 番剧主表
CREATE TABLE IF NOT EXISTS `anime` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `title` VARCHAR(255) NOT NULL,
    `type` ENUM('tv_series', 'movie', 'ova', 'other') NOT NULL DEFAULT 'tv_series',
    `image_url` VARCHAR(512) NULL,
    `season` INT NOT NULL DEFAULT 1,
    `episode_count` INT NULL DEFAULT NULL,
    `source_url` VARCHAR(512) NULL,
    `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    INDEX `idx_title` (`title`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 2. 分集表
CREATE TABLE IF NOT EXISTS `episode` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `source_id` BIGINT NOT NULL,
    `title` VARCHAR(255) NOT NULL,
    `episode_index` INT NOT NULL,
    `provider_episode_id` VARCHAR(255) NULL,
    `source_url` VARCHAR(512) NULL,
    `fetched_at` TIMESTAMP NULL DEFAULT NULL,
    `comment_count` INT NOT NULL DEFAULT 0,
    PRIMARY KEY (`id`),
    UNIQUE INDEX `idx_source_episode_unique` (`source_id` ASC, `episode_index` ASC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 3. 弹幕评论表
CREATE TABLE IF NOT EXISTS `comment` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `cid` VARCHAR(255) NOT NULL,
    `episode_id` BIGINT NOT NULL,
    `p` VARCHAR(255) NOT NULL,
    `m` TEXT NOT NULL,
    `t` DECIMAL(10, 2) NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE INDEX `idx_episode_cid_unique` (`episode_id` ASC, `cid` ASC),
    INDEX `idx_episode_time` (`episode_id` ASC, `t` ASC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 4. 用户表
CREATE TABLE IF NOT EXISTS `users` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `username` VARCHAR(50) NOT NULL,
    `hashed_password` VARCHAR(255) NOT NULL,
    `token` TEXT NULL,
    `token_update` TIMESTAMP NULL DEFAULT NULL,
    `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE INDEX `idx_username_unique` (`username` ASC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 5. 搜索源配置表
CREATE TABLE IF NOT EXISTS `scrapers` (
    `provider_name` VARCHAR(50) NOT NULL,
    `is_enabled` BOOLEAN NOT NULL DEFAULT TRUE,
    `display_order` INT NOT NULL DEFAULT 0,
    PRIMARY KEY (`provider_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 6. 番剧数据源表
CREATE TABLE IF NOT EXISTS `anime_sources` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `anime_id` BIGINT NOT NULL,
    `provider_name` VARCHAR(50) NOT NULL,
    `media_id` VARCHAR(255) NOT NULL,
    `is_favorited` BOOLEAN NOT NULL DEFAULT FALSE,
    `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE INDEX `idx_anime_provider_media_unique` (`anime_id` ASC, `provider_name` ASC, `media_id` ASC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 7. 番剧元数据表
CREATE TABLE IF NOT EXISTS `anime_metadata` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `anime_id` BIGINT NOT NULL,
    `tmdb_id` VARCHAR(50) NULL,
    `tmdb_episode_group_id` VARCHAR(50) NULL,
    `imdb_id` VARCHAR(50) NULL,
    `tvdb_id` VARCHAR(50) NULL,
    `douban_id` VARCHAR(50) NULL,
    `bangumi_id` VARCHAR(50) NULL,
    PRIMARY KEY (`id`),
    UNIQUE INDEX `idx_anime_id_unique` (`anime_id` ASC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 8. 配置表
CREATE TABLE IF NOT EXISTS `config` (
    `config_key` VARCHAR(100) NOT NULL,
    `config_value` TEXT NOT NULL,
    `description` TEXT NULL,
    PRIMARY KEY (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 9. 缓存数据表
CREATE TABLE IF NOT EXISTS `cache_data` (
    `cache_provider` VARCHAR(50) NULL,
    `cache_key` VARCHAR(255) NOT NULL,
    `cache_value` LONGTEXT NOT NULL,
    `expires_at` TIMESTAMP NOT NULL,
    PRIMARY KEY (`cache_key`),
    INDEX `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 10. API令牌表
CREATE TABLE IF NOT EXISTS `api_tokens` (
    `id` INT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL,
    `token` VARCHAR(50) NOT NULL,
    `is_enabled` BOOLEAN NOT NULL DEFAULT TRUE,
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `expires_at` TIMESTAMP NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE INDEX `idx_token_unique` (`token` ASC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 11. 令牌访问日志表
CREATE TABLE IF NOT EXISTS `token_access_logs` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `token_id` INT NOT NULL,
    `ip_address` VARCHAR(45) NOT NULL,
    `user_agent` TEXT NULL,
    `access_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `status` VARCHAR(50) NOT NULL,
    `path` VARCHAR(512) NULL,
    PRIMARY KEY (`id`),
    INDEX `idx_token_id_time` (`token_id` ASC, `access_time` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 12. UA规则表
CREATE TABLE IF NOT EXISTS `ua_rules` (
    `id` INT NOT NULL AUTO_INCREMENT,
    `ua_string` VARCHAR(255) NOT NULL,
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE INDEX `idx_ua_string_unique` (`ua_string` ASC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 13. Bangumi认证表
CREATE TABLE IF NOT EXISTS `bangumi_auth` (
    `user_id` BIGINT NOT NULL,
    `bangumi_user_id` INT NULL,
    `nickname` VARCHAR(255) NULL,
    `avatar_url` VARCHAR(512) NULL,
    `access_token` TEXT NOT NULL,
    `refresh_token` TEXT NULL,
    `expires_at` TIMESTAMP NULL DEFAULT NULL,
    `authorized_at` TIMESTAMP NULL DEFAULT NULL,
    PRIMARY KEY (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 14. OAuth状态表
CREATE TABLE IF NOT EXISTS `oauth_states` (
    `state_key` VARCHAR(100) NOT NULL,
    `user_id` BIGINT NOT NULL,
    `expires_at` TIMESTAMP NOT NULL,
    PRIMARY KEY (`state_key`),
    INDEX `idx_oauth_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 15. 番剧别名表
CREATE TABLE IF NOT EXISTS `anime_aliases` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `anime_id` BIGINT NOT NULL,
    `name_en` VARCHAR(255) NULL,
    `name_jp` VARCHAR(255) NULL,
    `name_romaji` VARCHAR(255) NULL,
    `alias_cn_1` VARCHAR(255) NULL,
    `alias_cn_2` VARCHAR(255) NULL,
    `alias_cn_3` VARCHAR(255) NULL,
    PRIMARY KEY (`id`),
    UNIQUE INDEX `idx_anime_id_unique` (`anime_id` ASC),
    INDEX `idx_anime_id` (`anime_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 16. TMDB分集映射表
CREATE TABLE IF NOT EXISTS `tmdb_episode_mapping` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `tmdb_tv_id` INT NOT NULL,
    `tmdb_episode_group_id` VARCHAR(50) NOT NULL,
    `tmdb_episode_id` INT NOT NULL,
    `tmdb_season_number` INT NOT NULL,
    `tmdb_episode_number` INT NOT NULL,
    `custom_season_number` INT NOT NULL,
    `custom_episode_number` INT NOT NULL,
    `absolute_episode_number` INT NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_group_episode_unique` (`tmdb_episode_group_id`, `tmdb_episode_id`),
    INDEX `idx_custom_season_episode` (`tmdb_tv_id`, `tmdb_episode_group_id`, `custom_season_number`, `custom_episode_number`),
    INDEX `idx_absolute_episode` (`tmdb_tv_id`, `tmdb_episode_group_id`, `absolute_episode_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 17. 定时任务表
CREATE TABLE IF NOT EXISTS `scheduled_tasks` (
    `id` VARCHAR(100) NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `job_type` VARCHAR(50) NOT NULL,
    `cron_expression` VARCHAR(100) NOT NULL,
    `is_enabled` BOOLEAN NOT NULL DEFAULT TRUE,
    `last_run_at` TIMESTAMP NULL DEFAULT NULL,
    `next_run_at` TIMESTAMP NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 18. 任务历史表
CREATE TABLE IF NOT EXISTS `task_history` (
    `id` VARCHAR(100) NOT NULL,
    `title` VARCHAR(255) NOT NULL,
    `status` VARCHAR(50) NOT NULL,
    `progress` INT NOT NULL DEFAULT 0,
    `description` TEXT NULL,
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `finished_at` TIMESTAMP NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    INDEX `idx_created_at` (`created_at` DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 显示创建结果
SELECT 'TiDB Cloud database and tables created successfully!' as Status;
SHOW TABLES;
