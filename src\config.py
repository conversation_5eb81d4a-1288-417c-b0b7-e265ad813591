import yaml
from pathlib import Path
from typing import Any, Dict, Optional

from pydantic import BaseModel

# 1. 为配置的不同部分创建 Pydantic 模型，提供类型提示和默认值
class ServerConfig(BaseModel):
    host: str = "0.0.0.0"
    port: int = 7768

class DatabaseConfig(BaseModel):
    host: str = "127.0.0.1"
    port: int = 3306
    user: str = "root"
    password: str = "password"
    name: str = "danmaku_db"
    # 数据库类型配置
    db_type: str = "auto"  # auto, mysql, tidb - 自动检测或手动指定
    # TLS/SSL配置
    ssl_enabled: bool = False  # 是否启用SSL/TLS连接
    ssl_verify_cert: bool = True  # 是否验证服务器证书
    ssl_ca_path: Optional[str] = None  # CA证书文件路径
    ssl_cert_path: Optional[str] = None  # 客户端证书文件路径
    ssl_key_path: Optional[str] = None  # 客户端私钥文件路径

class JWTConfig(BaseModel):
    secret_key: str = "a_very_secret_key_that_should_be_changed"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 1440 # 1 day

# 4. (新增) 初始管理员配置
class AdminConfig(BaseModel):
    initial_user: Optional[str] = None
    initial_password: Optional[str] = None

# 5. (新增) Bangumi OAuth 配置
class BangumiConfig(BaseModel):
    client_id: str = "bgm4222688b7532ef439"
    client_secret: str = "379c426b8f26b561642334445761361f"

# 2. 创建一个配置加载器，用于从 YAML 文件加载设置
def load_config_from_yaml() -> Dict[str, Any]:
    """从 YAML 文件加载配置"""
    # 在项目根目录的 config/ 文件夹下查找 config.yml
    yaml_file = Path(__file__).parent.parent / "config" / "config.yml"

    if not yaml_file.is_file():
        return {}

    with open(yaml_file, "r", encoding="utf-8") as f:
        return yaml.safe_load(f) or {}


# (新增) 豆瓣配置
class DoubanConfig(BaseModel):
    cookie: Optional[str] = None


# 3. 定义主设置类，它将聚合所有配置
class Settings:
    """应用程序设置类，从YAML配置文件加载配置"""

    def __init__(self):
        # 从YAML文件加载配置
        config_data = load_config_from_yaml()

        # 初始化各个配置模块
        self.server = ServerConfig(**(config_data.get('server', {})))
        self.database = DatabaseConfig(**(config_data.get('database', {})))
        self.jwt = JWTConfig(**(config_data.get('jwt', {})))
        self.admin = AdminConfig(**(config_data.get('admin', {})))
        self.bangumi = BangumiConfig(**(config_data.get('bangumi', {})))
        self.douban = DoubanConfig(**(config_data.get('douban', {})))


# 创建全局设置实例
settings = Settings()
